import {
  Table,
  Column,
  Model,
  DataType,
  HasMany,
  BeforeCreate,
  BeforeUpdate,
} from 'sequelize-typescript';
import { CustomerCoupon } from './customer-coupon.entity';
import { ApplicableScope } from '../common/Constant';

export interface CouponAttributes {
  /** 代金券唯一标识 */
  id: number;
  /** 类别，从字典获取 */
  type?: string;
  /** 售价 */
  price: number;
  /** 面值 */
  amount: number;
  /** 使用门槛 */
  threshold: number;
  /** 有效天数 */
  validDays?: number;
  /** 可用次数 */
  usageLimit?: number;
  /** 适用范围 */
  applicableScope: ApplicableScope;
  /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
  applicableServiceTypes?: string[];
  /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
  applicableServiceCategories?: number[];
  /** 适用服务ID列表，适用范围为指定服务时必填 */
  applicableServices?: number[];
  /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
  applicableProductTypes?: number[];
  /** 适用商品ID列表，适用范围为指定商品时必填 */
  applicableProducts?: number[];
  /** 权益描述 */
  description?: string;
  /** 是否启用 */
  isEnabled: boolean;
  /** 关联的用户代金券信息 */
  userCoupons?: CustomerCoupon[];
}

@Table({ tableName: 'coupons', timestamps: true, comment: '代金券表' })
export class Coupon
  extends Model<CouponAttributes>
  implements CouponAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '代金券唯一标识',
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '类别，从字典获取',
  })
  type?: string;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '售价',
  })
  price: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '面值',
  })
  amount: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '使用门槛',
  })
  threshold: number;

  @Column({
    type: DataType.INTEGER,
    comment: '有效天数',
  })
  validDays?: number;

  @Column({
    type: DataType.INTEGER,
    comment: '可用次数',
  })
  usageLimit?: number;

  @Column({
    type: DataType.ENUM(...Object.values(ApplicableScope)),
    allowNull: false,
    comment: '适用范围',
  })
  applicableScope: ApplicableScope;

  @Column({
    type: DataType.JSON,
    comment: '适用服务类型名称列表，适用范围为指定服务类型时必填',
  })
  applicableServiceTypes?: string[];

  @Column({
    type: DataType.JSON,
    comment: '适用服务品牌ID列表，适用范围为指定服务品牌时必填',
  })
  applicableServiceCategories?: number[];

  @Column({
    type: DataType.JSON,
    comment: '适用服务ID列表，适用范围为指定服务时必填',
  })
  applicableServices?: number[];

  @Column({
    type: DataType.JSON,
    comment: '适用商品类别ID列表，适用范围为指定商品类别时必填',
  })
  applicableProductTypes?: number[];

  @Column({
    type: DataType.JSON,
    comment: '适用商品ID列表，适用范围为指定商品时必填',
  })
  applicableProducts?: number[];

  @Column({
    type: DataType.TEXT,
    comment: '权益描述',
  })
  description?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否启用',
  })
  isEnabled: boolean;

  @HasMany(() => CustomerCoupon)
  userCoupons?: CustomerCoupon[];

  @BeforeCreate
  static async hashPassword(instance: Coupon) {
    // 暂时只不支持混合选择
    switch (instance.applicableScope) {
      case ApplicableScope.不限:
      case ApplicableScope.所有服务:
      case ApplicableScope.所有商品:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务类别:
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务品牌:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定商品类别:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定商品:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        break;
      default:
        break;
    }
  }

  @BeforeUpdate
  static async updatePassword(instance: Coupon) {
    // 范围发生变更时，清除多余的信息
    switch (instance.applicableScope) {
      case ApplicableScope.不限:
      case ApplicableScope.所有服务:
      case ApplicableScope.所有商品:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务类别:
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务品牌:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定服务:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定商品类别:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品ID列表，适用范围为指定商品时必填 */
        instance.applicableProducts = null;
        break;
      case ApplicableScope.指定商品:
        /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
        instance.applicableServiceTypes = null;
        /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
        instance.applicableServiceCategories = null;
        /** 适用服务ID列表，适用范围为指定服务时必填 */
        instance.applicableServices = null;
        /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
        instance.applicableProductTypes = null;
        break;
      default:
        break;
    }
  }
}
