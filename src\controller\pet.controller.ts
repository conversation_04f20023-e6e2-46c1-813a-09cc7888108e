import { Controller, Get, Inject, Param } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomerService } from '../service/customer.service';
import { CustomError } from '../error/custom.error';

@Controller('/pets')
export class PetController {
  @Inject()
  ctx: Context;

  @Inject()
  customerService: CustomerService;

  @Get('/customer/:customerId/count', { summary: '获取指定客户的宠物数量' })
  async getCustomerPetCount(@Param('customerId') customerId: number) {
    if (!customerId) {
      throw new CustomError('客户ID不能为空', 400);
    }

    // 验证客户是否存在
    const customer = await this.customerService.findById(customerId);
    if (!customer) {
      throw new CustomError('客户不存在', 404);
    }

    const count = await this.customerService.getPetCount(customerId);

    return {
      customerId,
      petCount: count,
    };
  }
}
