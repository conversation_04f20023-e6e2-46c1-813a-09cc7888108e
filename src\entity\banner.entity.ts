import { Table, Column, Model, DataType } from 'sequelize-typescript';

export enum JumpType {
  CUSTOM = 'custom',
  ACTIVITY = 'activity',
}

export interface BannerAttributes {
  /** 轮播图ID */
  id: number;
  /** 图片链接 */
  imageURL: string;
  /** 跳转类型（custom-自定义链接，activity-活动页，null-无跳转） */
  jumpType?: JumpType;
  /** 跳转链接（jumpType为custom时使用） */
  jumpLink?: string;
  /** 展示优先级（1-10） */
  priority: number;
}

@Table({ tableName: 'banners', timestamps: true, comment: '轮播图表' })
export class Banner
  extends Model<BannerAttributes>
  implements BannerAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '轮播图ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '图片链接',
  })
  imageURL: string;

  @Column({
    type: DataType.ENUM(...Object.values(JumpType)),
    allowNull: true,
    comment: '跳转类型（custom-自定义链接，activity-活动页，null-无跳转）',
  })
  jumpType: JumpType;

  @Column({
    type: DataType.STRING(255),
    comment: '跳转链接（jumpType为custom时使用）',
  })
  jumpLink: string;

  @Column({
    type: DataType.TINYINT,
    defaultValue: 5,
    comment: '展示优先级（1-10）',
  })
  priority: number;
}
