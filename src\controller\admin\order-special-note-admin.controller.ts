import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  Inject,
} from '@midwayjs/core';
import { OrderSpecialNoteService } from '../../service/order-special-note.service';
import { CustomError } from '../../error/custom.error';

@Controller('/admin/order-special-notes')
export class OrderSpecialNoteAdminController {
  @Inject()
  orderSpecialNoteService: OrderSpecialNoteService;

  @Get('/', { summary: '管理端查询所有订单特殊情况说明' })
  async findAll(
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10,
    @Query('orderId') orderId?: number,
    @Query('employeeId') employeeId?: number,
    @Query('keyword') keyword?: string
  ) {
    return await this.orderSpecialNoteService.findAllForAdmin(
      current,
      pageSize,
      orderId,
      employeeId,
      keyword
    );
  }

  @Get('/:id', { summary: '管理端查询特殊情况说明详情' })
  async findById(@Param('id') id: number) {
    const specialNote = await this.orderSpecialNoteService.findById(id);
    if (!specialNote) {
      throw new CustomError('特殊情况说明不存在', 404);
    }
    return specialNote;
  }

  @Get('/order/:orderId', { summary: '管理端查询指定订单的特殊情况说明' })
  async getByOrderId(@Param('orderId') orderId: number) {
    const specialNote = await this.orderSpecialNoteService.getSpecialNoteByOrderId(
      orderId
    );
    
    if (!specialNote) {
      return null; // 管理端查询时，如果没有特殊情况说明，返回null而不是抛出错误
    }

    return specialNote;
  }

  @Post('/sync-order-flag', { summary: '同步订单hasSpecialNote字段' })
  async syncOrderHasSpecialNoteField() {
    return await this.orderSpecialNoteService.syncOrderHasSpecialNoteField();
  }
}
