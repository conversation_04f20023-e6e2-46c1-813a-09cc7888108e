import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { SysMessage } from './sys-message.entity';
import { Customer } from './customer.entity';

export interface MessagePushLogAttributes {
  /** 记录ID */
  id: number;
  /** 消息ID */
  messageId: number;
  /** 用户ID */
  userId: number;
  /** 推送类型 */
  pushType: 'wechat' | 'sms' | 'email';
  /** 推送状态 */
  pushStatus: 'pending' | 'success' | 'failed';
  /** 推送结果 */
  pushResult?: string;
  /** 推送时间 */
  pushedAt?: Date;
  /** 创建时间 */
  createdAt?: Date;
  /** 关联消息信息 */
  sysMessage?: SysMessage;
  /** 关联用户信息 */
  user?: Customer;
}

@Table({
  tableName: 'message_push_logs',
  timestamps: true,
  updatedAt: false,
  comment: '消息推送记录表',
})
export class MessagePushLog
  extends Model<MessagePushLogAttributes>
  implements MessagePushLogAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '记录ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '消息ID',
  })
  @ForeignKey(() => SysMessage)
  messageId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '用户ID',
  })
  @ForeignKey(() => Customer)
  userId: number;

  @Column({
    type: DataType.ENUM('wechat', 'sms', 'email'),
    allowNull: false,
    comment: '推送类型',
  })
  pushType: 'wechat' | 'sms' | 'email';

  @Column({
    type: DataType.ENUM('pending', 'success', 'failed'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '推送状态',
  })
  pushStatus: 'pending' | 'success' | 'failed';

  @Column({
    type: DataType.TEXT,
    comment: '推送结果',
  })
  pushResult?: string;

  @Column({
    type: DataType.DATE,
    comment: '推送时间',
  })
  pushedAt?: Date;

  @BelongsTo(() => SysMessage, { onDelete: 'CASCADE' })
  sysMessage: SysMessage;

  @BelongsTo(() => Customer, { onDelete: 'CASCADE' })
  user: Customer;
}
