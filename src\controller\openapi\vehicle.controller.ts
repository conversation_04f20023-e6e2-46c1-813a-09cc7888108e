import {
  Controller,
  Get,
  Put,
  Body,
  Param,
  Inject,
  Query,
} from '@midwayjs/core';
import { VehicleService } from '../../service/vehicle.service';
import { EmployeeService } from '../../service/employee.service';
import { VehicleUpdateDto } from '../../dto/vehicle-update.dto';
import { CustomError } from '../../error/custom.error';
import { Validate } from '@midwayjs/validate';

@Controller('/openapi/vehicles')
export class OpenapiVehicleController {
  @Inject()
  vehicleService: VehicleService;

  @Inject()
  employeeService: EmployeeService;

  @Get('/employee/:employeeId', { summary: '获取员工关联的车辆信息' })
  async getEmployeeVehicle(@Param('employeeId') employeeId: number) {
    const employee = await this.employeeService.findById(employeeId);
    if (!employee) {
      throw new CustomError('员工不存在', 404);
    }

    if (!employee.vehicleId) {
      throw new CustomError('该员工未分配车辆', 400);
    }

    const vehicle = await this.vehicleService.getVehicleDetail(employee.vehicleId);
    if (!vehicle) {
      throw new CustomError('车辆信息不存在', 404);
    }

    return vehicle;
  }

  @Put('/:vehicleId/info', { summary: '员工端更新车辆信息' })
  @Validate()
  async updateVehicleInfo(
    @Param('vehicleId') vehicleId: number,
    @Body() updateData: VehicleUpdateDto,
    @Query('employeeId') employeeId: number
  ) {
    if (!employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }

    // 验证员工是否存在
    const employee = await this.employeeService.findById(employeeId);
    if (!employee) {
      throw new CustomError('员工不存在', 404);
    }

    // 验证车辆是否存在
    const vehicle = await this.vehicleService.findById(vehicleId);
    if (!vehicle) {
      throw new CustomError('车辆不存在', 404);
    }

    // 验证员工是否有权限更新该车辆（只能更新与自己绑定的车辆）
    if (employee.vehicleId !== vehicleId) {
      throw new CustomError('您只能更新与自己绑定的车辆信息', 403);
    }
    
    await this.vehicleService.updateVehicleInfo(vehicleId, employeeId, updateData);
    
    return {
      success: true,
      message: '车辆信息更新成功',
    };
  }

  @Get('/:vehicleId', { summary: '获取车辆详细信息' })
  async getVehicleDetail(@Param('vehicleId') vehicleId: number) {
    const vehicle = await this.vehicleService.getVehicleDetail(vehicleId);
    if (!vehicle) {
      throw new CustomError('车辆不存在', 404);
    }

    return vehicle;
  }
}
