import { Provide, Inject } from '@midwayjs/core';
import { Review } from '../entity/review.entity';
import { BaseService } from '../common/BaseService';
import { Order } from '../entity/order.entity';
import { Customer } from '../entity/customer.entity';
import { Service } from '../entity/service.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Employee } from '../entity/employee.entity';
import { CustomError } from '../error/custom.error';
import { OrderStatus } from '../common/Constant';
import { Op, fn, col } from 'sequelize';
import { EmployeeService } from './employee.service';

@Provide()
export class ReviewService extends BaseService<Review> {
  @Inject()
  employeeService: EmployeeService;

  constructor() {
    super('评价');
  }

  getModel() {
    return Review;
  }

  /**
   * 创建评价
   * @param customerId 客户ID
   * @param orderId 订单ID
   * @param rating 评分
   * @param comment 评价内容
   * @param photoURLs 图片URL数组
   */
  async createReview(
    customerId: number,
    orderId: number,
    rating: number,
    comment: string,
    photoURLs?: string[]
  ) {
    // 检查订单是否存在且属于该客户
    const order = await Order.findOne({
      where: { id: orderId },
    });

    if (!order) {
      throw new CustomError('订单不存在或不属于该客户', 404);
    }

    // 检查订单状态是否为已完成
    if (order.status !== OrderStatus.已完成) {
      throw new CustomError('只有已完成的订单才能评价', 400);
    }

    // 检查是否已经评价过
    const existingReview = await Review.findOne({
      where: { orderId },
    });

    if (existingReview) {
      throw new CustomError('该订单已存在评价', 409);
    }

    // 创建评价
    const review = await Review.create({
      customerId,
      orderId,
      rating,
      comment,
      photoURLs: photoURLs || [],
    });

    // 更新订单状态为已评价
    await order.update({ status: OrderStatus.已评价 });

    // 如果订单有关联的员工，更新员工评分
    if (order.employeeId) {
      try {
        await this.employeeService.updateEmployeeRating(order.employeeId);
      } catch (error) {
        console.error('更新员工评分失败:', error);
        // 不影响评价创建的主流程，只记录错误
      }
    }

    return await Review.findByPk(review.id, {
      include: [
        {
          model: Order,
          include: [
            {
              model: OrderDetail,
              include: [{ model: Service }],
            },
          ],
        },
      ],
    });
  }

  /**
   * 根据订单ID获取评价
   * @param orderId 订单ID
   */
  async getReviewByOrderId(orderId: number) {
    const review = await Review.findOne({
      where: { orderId },
      include: [
        {
          model: Order,
          include: [
            {
              model: OrderDetail,
              include: [{ model: Service }],
            },
          ],
        },
        { model: Customer },
      ],
    });

    return review;
  }

  /**
   * 获取客户评价列表
   * @param customerId 客户ID
   * @param page 页码
   * @param pageSize 每页数量
   */
  async getCustomerReviews(customerId: number, page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;

    return await this.findAll({
      query: { customerId },
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Order,
          include: [
            {
              model: OrderDetail,
              include: [{ model: Service }],
            },
          ],
        },
      ],
    });
  }

  /**
   * 获取服务评价列表和统计信息
   * @param serviceId 服务ID
   * @param page 页码
   * @param pageSize 每页数量
   */
  async getServiceReviews(serviceId: number, page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;

    // 获取评价列表 - 通过订单详情关联查询
    const reviews = await this.findAll({
      query: {},
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Order,
          required: true,
          include: [
            {
              model: OrderDetail,
              required: true,
              where: { serviceId },
              include: [{ model: Service }],
            },
          ],
        },
        { model: Customer },
      ],
    });

    // 计算评价统计信息
    const stats = await this.getServiceReviewStats(serviceId);

    return {
      ...reviews,
      ...stats,
    };
  }

  /**
   * 获取服务评价统计信息
   * @param serviceId 服务ID
   */
  async getServiceReviewStats(serviceId: number) {
    // 通过订单详情关联查询该服务的所有评价
    const reviews = await Review.findAll({
      include: [
        {
          model: Order,
          required: true,
          include: [
            {
              model: OrderDetail,
              required: true,
              where: { serviceId },
            },
          ],
        },
      ],
    });

    if (reviews.length === 0) {
      return {
        averageRating: 0,
        ratingDistribution: { '5': 0, '4': 0, '3': 0, '2': 0, '1': 0 },
      };
    }

    // 计算平均评分
    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = Number((totalRating / reviews.length).toFixed(1));

    // 计算评分分布
    const ratingDistribution = { '5': 0, '4': 0, '3': 0, '2': 0, '1': 0 };
    reviews.forEach(review => {
      ratingDistribution[review.rating.toString()]++;
    });

    return {
      averageRating,
      ratingDistribution,
    };
  }

  /**
   * 更新评价
   * @param customerId 客户ID
   * @param reviewId 评价ID
   * @param updateData 更新数据
   */
  async updateReview(
    customerId: number,
    reviewId: number,
    updateData: {
      rating?: number;
      comment?: string;
      photoURLs?: string[];
    }
  ) {
    const review = await Review.findOne({
      where: { id: reviewId, customerId },
      include: [{ model: Order }],
    });

    if (!review) {
      throw new CustomError('评价不存在或不属于该客户', 404);
    }

    await review.update(updateData);

    // 如果更新了评分且订单有关联的员工，重新计算员工评分
    if (updateData.rating && review.order?.employeeId) {
      try {
        await this.employeeService.updateEmployeeRating(
          review.order.employeeId
        );
      } catch (error) {
        console.error('更新员工评分失败:', error);
        // 不影响评价更新的主流程，只记录错误
      }
    }

    return await Review.findByPk(reviewId, {
      include: [
        {
          model: Order,
          include: [
            {
              model: OrderDetail,
              include: [{ model: Service }],
            },
          ],
        },
      ],
    });
  }

  /**
   * 删除评价
   * @param customerId 客户ID
   * @param reviewId 评价ID
   */
  async deleteReview(customerId: number, reviewId: number) {
    const review = await Review.findOne({
      where: { id: reviewId, customerId },
      include: [{ model: Order }],
    });

    if (!review) {
      throw new CustomError('评价不存在或不属于该客户', 404);
    }

    const employeeId = review.order?.employeeId;

    // 删除评价
    await review.destroy();

    // 将订单状态改回已完成
    if (review.order) {
      await review.order.update({ status: OrderStatus.已完成 });
    }

    // 如果订单有关联的员工，重新计算员工评分
    if (employeeId) {
      try {
        await this.employeeService.updateEmployeeRating(employeeId);
      } catch (error) {
        console.error('更新员工评分失败:', error);
        // 不影响评价删除的主流程，只记录错误
      }
    }

    return true;
  }

  /**
   * 获取评价统计概览
   * @param startDate 开始日期
   * @param endDate 结束日期
   */
  async getReviewStatistics(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 总评价数
    const totalReviews = await Review.count({ where: whereCondition });

    // 今日评价数
    const today = new Date();
    const todayStart = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000 - 1);
    const todayReviews = await Review.count({
      where: {
        createdAt: {
          [Op.between]: [todayStart, todayEnd],
        },
      },
    });

    // 本周评价数
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay());
    weekStart.setHours(0, 0, 0, 0);
    const thisWeekReviews = await Review.count({
      where: {
        createdAt: {
          [Op.gte]: weekStart,
        },
      },
    });

    // 本月评价数
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const thisMonthReviews = await Review.count({
      where: {
        createdAt: {
          [Op.gte]: monthStart,
        },
      },
    });

    // 平均评分和好评率
    let averageRating = 0;
    let positiveRate = 0;

    if (totalReviews > 0) {
      const reviews = await Review.findAll({
        where: whereCondition,
        attributes: ['rating'],
      });

      const totalRating = reviews.reduce(
        (sum, review) => sum + review.rating,
        0
      );
      averageRating = Number((totalRating / reviews.length).toFixed(1));

      const positiveReviews = reviews.filter(
        review => review.rating >= 4
      ).length;
      positiveRate = Number(
        ((positiveReviews / reviews.length) * 100).toFixed(1)
      );
    }

    return {
      totalReviews,
      todayReviews,
      averageRating,
      positiveRate,
      thisWeekReviews,
      thisMonthReviews,
    };
  }

  /**
   * 获取评价趋势数据
   * @param startDate 开始日期
   * @param endDate 结束日期
   */
  async getReviewTrend(startDate: string, endDate: string) {
    const reviews = await Review.findAll({
      where: {
        createdAt: {
          [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
        },
      },
      attributes: [
        [fn('DATE', col('createdAt')), 'date'],
        [fn('COUNT', col('id')), 'reviews'],
        [fn('AVG', col('rating')), 'averageRating'],
      ],
      group: [fn('DATE', col('createdAt'))],
      order: [[fn('DATE', col('createdAt')), 'ASC']],
      raw: true,
    });

    return reviews.map((item: any) => ({
      date: item.date,
      reviews: parseInt(item.reviews),
      averageRating: Number(parseFloat(item.averageRating).toFixed(1)),
    }));
  }

  /**
   * 获取评分分布统计
   * @param startDate 开始日期
   * @param endDate 结束日期
   */
  async getRatingDistribution(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    const distribution = await Review.findAll({
      where: whereCondition,
      attributes: ['rating', [fn('COUNT', col('id')), 'count']],
      group: ['rating'],
      order: [['rating', 'DESC']],
      raw: true,
    });

    const totalCount = await Review.count({ where: whereCondition });

    return distribution.map((item: any) => ({
      rating: item.rating,
      count: parseInt(item.count),
      percentage:
        totalCount > 0
          ? Number(((parseInt(item.count) / totalCount) * 100).toFixed(1))
          : 0,
    }));
  }

  /**
   * 获取员工评分排行
   * @param limit 返回条数
   * @param startDate 开始日期
   * @param endDate 结束日期
   */
  async getEmployeeRanking(limit = 10, startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    const rankings = await Review.findAll({
      where: whereCondition,
      include: [
        {
          model: Order,
          required: true,
          where: {
            employeeId: {
              [Op.ne]: null,
            },
          },
          include: [
            {
              model: Employee,
              required: true,
              attributes: ['id', 'name', 'level'],
            },
          ],
        },
      ],
      attributes: [
        [col('order.employeeId'), 'employeeId'],
        [col('order.employee.name'), 'employeeName'],
        [col('order.employee.level'), 'level'],
        [fn('AVG', col('rating')), 'averageRating'],
        [fn('COUNT', col('Review.id')), 'reviewCount'],
      ],
      group: [
        'order.employeeId',
        'order.employee.name',
        'order.employee.level',
      ],
      order: [[fn('AVG', col('rating')), 'DESC']],
      limit,
      raw: true,
    });

    return rankings.map((item: any) => ({
      employeeId: item.employeeId,
      employeeName: item.employeeName,
      averageRating: Number(parseFloat(item.averageRating).toFixed(1)),
      reviewCount: parseInt(item.reviewCount),
      level: item.level,
    }));
  }
}
