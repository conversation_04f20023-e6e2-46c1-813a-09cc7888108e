import { OrderAttributes } from '../entity';
import * as dayjs from 'dayjs';

/** 接单成功提醒消息模板 */
export const orderConfirmationMessage = (order: OrderAttributes) => {
  return {
    touser: '', // 会在发送时替换
    template_id: '', // 会在发送时替换
    page: 'pages/serviceOrder/index',
    data: {
      thing3: {
        value: '您的订单已被商家接单',
        color: '#173177',
      },
      thing2: {
        value: order.employee?.name || '未知',
      },
      phone_number6: {
        value: order.employee?.phone || '未知',
      },
      character_string8: {
        value: order.sn,
      },
    },
  };
};

/** 服务进度提醒消息模板 */
export const orderStatusChangeMessage = (
  order: OrderAttributes,
  msg: string
) => {
  return {
    touser: '', // 会在发送时替换
    template_id: '', // 会在发送时替换
    page: 'pages/serviceOrder/index',
    data: {
      character_string11: {
        value: order.sn,
      },
      thing4: {
        value: order.orderDetails[0].service.serviceName,
      },
      thing2: {
        value: order.orderDetails[0].pet.name,
      },
      phrase7: {
        value: order.status,
      },
      thing9: {
        value: msg || '订单已更新',
      },
    },
  };
};

/** 服务时间更改通知模板 */
export const orderTimeChangeMessage = (order: OrderAttributes) => {
  return {
    touser: '', // 会在发送时替换
    template_id: '', // 会在发送时替换
    page: 'pages/serviceOrder/index',
    data: {
      thing1: {
        value: order.orderDetails[0].service.serviceName,
      },
      time2: {
        value: dayjs(order.serviceTime).format('YYYY-MM-DD HH:mm'),
        // value: order.serviceTime,
      },
      thing3: {
        value: '服务时间已更新，请提前预留时间',
      },
    },
  };
};

/** 服务完成通知模板 */
export const orderCompletionMessage = (order: OrderAttributes) => {
  return {
    touser: '', // 会在发送时替换
    template_id: '', // 会在发送时替换
    page: 'pages/serviceOrder/index',
    data: {
      thing6: {
        value: order.orderDetails[0].service.serviceName,
      },
      thing2: {
        value: order.orderDetails[0].pet.name,
      },
      time3: {
        value: dayjs(new Date()).format('YYYY-MM-DD HH:mm'),
        // value: order.serviceTime,
      },
      thing5: {
        value: '本次服务已完成，期待您的下次光临',
      },
    },
  };
};
