import { Rule, RuleType } from '@midwayjs/validate';

export class GetMessageListDTO {
  @Rule(RuleType.string().optional())
  type?: 'system' | 'platform' | 'order';

  @Rule(RuleType.number().integer().min(1).optional().default(1))
  page?: number;

  @Rule(RuleType.number().integer().min(1).max(100).optional().default(20))
  limit?: number;
}

export class CreateMessageDTO {
  @Rule(RuleType.number().integer().required())
  userId: number;

  @Rule(RuleType.string().valid('system', 'platform', 'order').required())
  type: 'system' | 'platform' | 'order';

  @Rule(RuleType.string().max(255).required())
  title: string;

  @Rule(RuleType.string().required())
  content: string;

  @Rule(RuleType.object().optional())
  extraData?: any;
}

export class CreateMessageFromTemplateDTO {
  @Rule(RuleType.string().required())
  templateCode: string;

  @Rule(RuleType.number().integer().required())
  userId: number;

  @Rule(RuleType.object().optional().default({}))
  variables?: Record<string, any>;
}
