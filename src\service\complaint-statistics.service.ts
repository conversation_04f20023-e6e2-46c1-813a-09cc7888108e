import { ILogger, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Op, fn, col, literal } from 'sequelize';
import { Complaint, Customer, Employee } from '../entity';
import { ComplaintStatus } from '../common/Constant';

@Provide()
export class ComplaintStatisticsService {
  @Inject()
  ctx: Context;

  @Inject()
  logger: ILogger;

  /**
   * 获取投诉建议概览统计
   */
  async getComplaintOverview(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    // 总投诉建议数
    const totalComplaints = await Complaint.count({ where: whereCondition });

    // 今日投诉建议数
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayComplaints = await Complaint.count({
      where: {
        ...whereCondition,
        createdAt: {
          [Op.between]: [today, tomorrow],
        },
      },
    });

    // 本周投诉建议数
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);

    const weekComplaints = await Complaint.count({
      where: {
        ...whereCondition,
        createdAt: {
          [Op.gte]: weekStart,
        },
      },
    });

    // 本月投诉建议数
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 1);

    const monthComplaints = await Complaint.count({
      where: {
        ...whereCondition,
        createdAt: {
          [Op.between]: [monthStart, monthEnd],
        },
      },
    });

    // 各状态统计
    const statusStats = await Complaint.findAll({
      where: whereCondition,
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    });

    // 各类型统计
    const categoryStats = await Complaint.findAll({
      where: whereCondition,
      attributes: ['category', [fn('COUNT', col('id')), 'count']],
      group: ['category'],
      raw: true,
    });

    // 各子类型统计
    const subCategoryStats = await Complaint.findAll({
      where: whereCondition,
      attributes: ['subCategory', [fn('COUNT', col('id')), 'count']],
      group: ['subCategory'],
      raw: true,
    });

    // 处理率统计
    const processedCount = await Complaint.count({
      where: {
        ...whereCondition,
        status: {
          [Op.in]: [ComplaintStatus.RESOLVED, ComplaintStatus.CLOSED],
        },
      },
    });

    const processRate =
      totalComplaints > 0
        ? Number(((processedCount / totalComplaints) * 100).toFixed(1))
        : 0;

    return {
      complaintStats: {
        total: totalComplaints,
        today: todayComplaints,
        week: weekComplaints,
        month: monthComplaints,
        processedCount,
        processRate,
      },
      statusStats: statusStats.map((item: any) => ({
        status: item.status,
        count: parseInt(item.count),
        percentage:
          totalComplaints > 0
            ? Number(
                ((parseInt(item.count) / totalComplaints) * 100).toFixed(1)
              )
            : 0,
      })),
      categoryStats: categoryStats.map((item: any) => ({
        category: item.category,
        count: parseInt(item.count),
        percentage:
          totalComplaints > 0
            ? Number(
                ((parseInt(item.count) / totalComplaints) * 100).toFixed(1)
              )
            : 0,
      })),
      subCategoryStats: subCategoryStats.map((item: any) => ({
        subCategory: item.subCategory,
        count: parseInt(item.count),
        percentage:
          totalComplaints > 0
            ? Number(
                ((parseInt(item.count) / totalComplaints) * 100).toFixed(1)
              )
            : 0,
      })),
    };
  }

  /**
   * 获取投诉建议趋势统计
   */
  async getComplaintTrend(
    startDate: string,
    endDate: string,
    periodType: 'day' | 'week' | 'month' = 'day'
  ) {
    const whereCondition: any = {
      createdAt: {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      },
    };

    let groupBy: string;
    let selectField: string;

    switch (periodType) {
      case 'day':
        groupBy = "DATE_FORMAT(createdAt, '%Y-%m-%d')";
        selectField = 'date';
        break;
      case 'week':
        groupBy = "DATE_FORMAT(createdAt, '%Y-%u')";
        selectField = 'week';
        break;
      case 'month':
        groupBy = "DATE_FORMAT(createdAt, '%Y-%m')";
        selectField = 'month';
        break;
      default:
        groupBy = "DATE_FORMAT(createdAt, '%Y-%m-%d')";
        selectField = 'date';
    }

    const trendData = await Complaint.findAll({
      where: whereCondition,
      attributes: [
        [literal(groupBy), selectField],
        [fn('COUNT', col('id')), 'totalCount'],
        [
          fn(
            'SUM',
            literal(
              "CASE WHEN Complaint.category = 'complaint' THEN 1 ELSE 0 END"
            )
          ),
          'complaintCount',
        ],
        [
          fn(
            'SUM',
            literal(
              "CASE WHEN Complaint.category = 'suggestion' THEN 1 ELSE 0 END"
            )
          ),
          'suggestionCount',
        ],
        [
          fn(
            'SUM',
            literal("CASE WHEN Complaint.status = 'resolved' THEN 1 ELSE 0 END")
          ),
          'resolvedCount',
        ],
      ],
      group: [literal(groupBy) as any],
      order: [[literal(groupBy) as any, 'ASC']],
      raw: true,
    });

    return trendData.map((item: any) => ({
      period: item[selectField],
      totalCount: parseInt(item.totalCount),
      complaintCount: parseInt(item.complaintCount),
      suggestionCount: parseInt(item.suggestionCount),
      resolvedCount: parseInt(item.resolvedCount),
      resolveRate:
        parseInt(item.totalCount) > 0
          ? Number(
              (
                (parseInt(item.resolvedCount) / parseInt(item.totalCount)) *
                100
              ).toFixed(1)
            )
          : 0,
    }));
  }

  /**
   * 获取客户投诉建议统计
   */
  async getCustomerComplaintStatistics(
    startDate?: string,
    endDate?: string,
    page = 1,
    pageSize = 20,
    sortBy: 'complaintCount' | 'suggestionCount' | 'totalCount' = 'totalCount',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    const customerStats = await Complaint.findAll({
      where: whereCondition,
      attributes: [
        'customerId',
        [fn('COUNT', col('Complaint.id')), 'totalCount'],
        [
          fn(
            'SUM',
            literal(
              "CASE WHEN Complaint.category = 'complaint' THEN 1 ELSE 0 END"
            )
          ),
          'complaintCount',
        ],
        [
          fn(
            'SUM',
            literal(
              "CASE WHEN Complaint.category = 'suggestion' THEN 1 ELSE 0 END"
            )
          ),
          'suggestionCount',
        ],
        [
          fn(
            'SUM',
            literal("CASE WHEN Complaint.status = 'resolved' THEN 1 ELSE 0 END")
          ),
          'resolvedCount',
        ],
      ],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar', 'memberStatus'],
          required: true,
        },
      ],
      group: ['customerId', 'customer.id'],
      order: [[fn('COUNT', col('Complaint.id')), sortOrder.toUpperCase()]],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      subQuery: false,
    });

    // 获取总数
    const totalCount = await Complaint.findAll({
      where: whereCondition,
      attributes: ['customerId', [fn('COUNT', col('id')), 'count']],
      group: ['customerId'],
      raw: true,
    });

    return {
      list: customerStats.map((item: any) => ({
        customerId: item.customerId,
        customerName: item.customer?.nickname,
        customerPhone: item.customer?.phone,
        customerAvatar: item.customer?.avatar,
        memberStatus: item.customer?.memberStatus,
        totalCount: parseInt(item.get('totalCount')),
        complaintCount: parseInt(item.get('complaintCount')),
        suggestionCount: parseInt(item.get('suggestionCount')),
        resolvedCount: parseInt(item.get('resolvedCount')),
        resolveRate:
          parseInt(item.get('totalCount')) > 0
            ? Number(
                (
                  (parseInt(item.get('resolvedCount')) /
                    parseInt(item.get('totalCount'))) *
                  100
                ).toFixed(1)
              )
            : 0,
      })),
      total: totalCount.length,
      page,
      pageSize,
    };
  }

  /**
   * 获取员工相关投诉统计
   */
  async getEmployeeComplaintStatistics(
    startDate?: string,
    endDate?: string,
    page = 1,
    pageSize = 20,
    sortBy:
      | 'complaintCount'
      | 'resolvedCount'
      | 'resolveRate' = 'complaintCount',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    const whereCondition: any = {
      subCategory: 'employee',
      employeeId: { [Op.ne]: null },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    const employeeStats = await Complaint.findAll({
      where: whereCondition,
      attributes: [
        'employeeId',
        [fn('COUNT', col('Complaint.id')), 'complaintCount'],
        [
          fn(
            'SUM',
            literal("CASE WHEN Complaint.status = 'resolved' THEN 1 ELSE 0 END")
          ),
          'resolvedCount',
        ],
        [
          fn(
            'SUM',
            literal("CASE WHEN Complaint.status = 'pending' THEN 1 ELSE 0 END")
          ),
          'pendingCount',
        ],
      ],
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar', 'rating'],
          required: true,
        },
      ],
      group: ['employeeId', 'employee.id'],
      order: [[fn('COUNT', col('Complaint.id')), sortOrder.toUpperCase()]],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      subQuery: false,
    });

    // 获取总数
    const totalCount = await Complaint.findAll({
      where: whereCondition,
      attributes: ['employeeId', [fn('COUNT', col('id')), 'count']],
      group: ['employeeId'],
      raw: true,
    });

    return {
      list: employeeStats.map((item: any) => ({
        employeeId: item.employeeId,
        employeeName: item.employee?.name,
        employeePhone: item.employee?.phone,
        employeeAvatar: item.employee?.avatar,
        employeeRating: item.employee?.rating,
        complaintCount: parseInt(item.get('complaintCount')),
        resolvedCount: parseInt(item.get('resolvedCount')),
        pendingCount: parseInt(item.get('pendingCount')),
        resolveRate:
          parseInt(item.get('complaintCount')) > 0
            ? Number(
                (
                  (parseInt(item.get('resolvedCount')) /
                    parseInt(item.get('complaintCount'))) *
                  100
                ).toFixed(1)
              )
            : 0,
      })),
      total: totalCount.length,
      page,
      pageSize,
    };
  }

  /**
   * 获取处理效率统计
   */
  async getProcessingEfficiencyStats(startDate?: string, endDate?: string) {
    const whereCondition: any = {
      status: { [Op.in]: [ComplaintStatus.RESOLVED, ComplaintStatus.CLOSED] },
      handledAt: { [Op.ne]: null },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    const processedComplaints = await Complaint.findAll({
      where: whereCondition,
      attributes: [
        'id',
        'createdAt',
        'handledAt',
        'category',
        'subCategory',
        'status',
        [
          literal('TIMESTAMPDIFF(HOUR, createdAt, handledAt)'),
          'processingHours',
        ],
      ],
      raw: true,
    });

    if (processedComplaints.length === 0) {
      return {
        averageProcessingHours: 0,
        fastProcessingCount: 0,
        slowProcessingCount: 0,
        categoryEfficiency: [],
        subCategoryEfficiency: [],
      };
    }

    // 计算平均处理时间
    const totalHours = processedComplaints.reduce(
      (sum, item: any) => sum + (parseInt(item.processingHours) || 0),
      0
    );
    const averageProcessingHours = Number(
      (totalHours / processedComplaints.length).toFixed(1)
    );

    // 快速处理（24小时内）和慢速处理（超过72小时）统计
    const fastProcessingCount = processedComplaints.filter(
      (item: any) => parseInt(item.processingHours) <= 24
    ).length;
    const slowProcessingCount = processedComplaints.filter(
      (item: any) => parseInt(item.processingHours) > 72
    ).length;

    // 按类型统计处理效率
    const categoryEfficiency = await this.calculateEfficiencyByField(
      processedComplaints,
      'category'
    );
    const subCategoryEfficiency = await this.calculateEfficiencyByField(
      processedComplaints,
      'subCategory'
    );

    return {
      averageProcessingHours,
      fastProcessingCount,
      slowProcessingCount,
      fastProcessingRate: Number(
        ((fastProcessingCount / processedComplaints.length) * 100).toFixed(1)
      ),
      slowProcessingRate: Number(
        ((slowProcessingCount / processedComplaints.length) * 100).toFixed(1)
      ),
      categoryEfficiency,
      subCategoryEfficiency,
    };
  }

  /**
   * 按字段计算处理效率
   */
  private calculateEfficiencyByField(data: any[], field: string) {
    const grouped = data.reduce((acc, item) => {
      const key = item[field];
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(parseInt(item.processingHours) || 0);
      return acc;
    }, {});

    return Object.keys(grouped).map(key => ({
      [field]: key,
      count: grouped[key].length,
      averageHours: Number(
        (
          grouped[key].reduce((sum, hours) => sum + hours, 0) /
          grouped[key].length
        ).toFixed(1)
      ),
      fastCount: grouped[key].filter(hours => hours <= 24).length,
      slowCount: grouped[key].filter(hours => hours > 72).length,
    }));
  }

  /**
   * 获取热点问题分析
   */
  async getHotIssuesAnalysis(startDate?: string, endDate?: string, limit = 10) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    // 关键词分析（基于标题和内容）
    const complaints = await Complaint.findAll({
      where: whereCondition,
      attributes: ['title', 'content', 'category', 'subCategory'],
    });

    // 简单的关键词提取（实际项目中可能需要更复杂的NLP处理）
    const keywordCount = {};
    const commonWords = [
      '的',
      '了',
      '是',
      '在',
      '有',
      '和',
      '我',
      '你',
      '他',
      '她',
      '它',
      '我们',
      '你们',
      '他们',
    ];

    complaints.forEach(complaint => {
      const text = (complaint.title + ' ' + complaint.content).toLowerCase();
      const words = text.match(/[\u4e00-\u9fa5]+/g) || [];

      words.forEach((word: any) => {
        if (word.length >= 2 && !commonWords.includes(word)) {
          keywordCount[word] = (keywordCount[word] || 0) + 1;
        }
      });
    });

    const hotKeywords = Object.entries(keywordCount)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, limit)
      .map(([keyword, count]) => ({ keyword, count }));

    // 按子类型统计热点问题
    const subCategoryHotIssues = await Complaint.findAll({
      where: whereCondition,
      attributes: ['subCategory', [fn('COUNT', col('id')), 'count']],
      group: ['subCategory'],
      order: [[fn('COUNT', col('id')), 'DESC']],
      limit,
      raw: true,
    });

    return {
      hotKeywords,
      subCategoryHotIssues: subCategoryHotIssues.map((item: any) => ({
        subCategory: item.subCategory,
        count: parseInt(item.count),
      })),
    };
  }

  /**
   * 获取处理人员统计
   */
  async getHandlerStatistics(
    startDate?: string,
    endDate?: string,
    page = 1,
    pageSize = 20,
    sortBy:
      | 'handledCount'
      | 'avgProcessingHours'
      | 'resolveRate' = 'handledCount',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    const whereCondition: any = {
      status: { [Op.in]: [ComplaintStatus.RESOLVED, ComplaintStatus.CLOSED] },
      handlerId: { [Op.ne]: null },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    const handlerStats = await Complaint.findAll({
      where: whereCondition,
      attributes: [
        'handlerId',
        [fn('COUNT', col('id')), 'handledCount'],
        [
          fn('AVG', literal('TIMESTAMPDIFF(HOUR, createdAt, handledAt)')),
          'avgProcessingHours',
        ],
        [
          fn(
            'SUM',
            literal("CASE WHEN Complaint.status = 'resolved' THEN 1 ELSE 0 END")
          ),
          'resolvedCount',
        ],
      ],
      group: ['handlerId'],
      order: [[fn('COUNT', col('id')), sortOrder.toUpperCase()]],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      raw: true,
    });

    // 获取总数
    const totalCount = await Complaint.findAll({
      where: whereCondition,
      attributes: ['handlerId', [fn('COUNT', col('id')), 'count']],
      group: ['handlerId'],
      raw: true,
    });

    return {
      list: handlerStats.map((item: any) => ({
        handlerId: item.handlerId,
        handledCount: parseInt(item.handledCount),
        avgProcessingHours: parseFloat(item.avgProcessingHours || '0'),
        resolvedCount: parseInt(item.resolvedCount),
        resolveRate:
          parseInt(item.handledCount) > 0
            ? Number(
                (
                  (parseInt(item.resolvedCount) / parseInt(item.handledCount)) *
                  100
                ).toFixed(1)
              )
            : 0,
      })),
      total: totalCount.length,
      page,
      pageSize,
    };
  }

  /**
   * 获取时间分布统计
   */
  async getTimeDistributionStats(
    startDate?: string,
    endDate?: string,
    timeType: 'hour' | 'weekday' | 'month' = 'hour'
  ) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    let groupBy: string;
    let selectField: string;

    switch (timeType) {
      case 'hour':
        groupBy = 'HOUR(createdAt)';
        selectField = 'hour';
        break;
      case 'weekday':
        groupBy = 'WEEKDAY(createdAt)';
        selectField = 'weekday';
        break;
      case 'month':
        groupBy = 'MONTH(createdAt)';
        selectField = 'month';
        break;
      default:
        groupBy = 'HOUR(createdAt)';
        selectField = 'hour';
    }

    const timeDistribution = await Complaint.findAll({
      where: whereCondition,
      attributes: [
        [literal(groupBy), selectField],
        [fn('COUNT', col('id')), 'count'],
        [
          fn(
            'SUM',
            literal(
              "CASE WHEN Complaint.category = 'complaint' THEN 1 ELSE 0 END"
            )
          ),
          'complaintCount',
        ],
        [
          fn(
            'SUM',
            literal(
              "CASE WHEN Complaint.category = 'suggestion' THEN 1 ELSE 0 END"
            )
          ),
          'suggestionCount',
        ],
      ],
      group: [literal(groupBy) as any],
      order: [[literal(groupBy) as any, 'ASC']],
      raw: true,
    });

    return timeDistribution.map((item: any) => {
      let label = item[selectField];

      if (timeType === 'hour') {
        label = `${item[selectField]}:00-${(item[selectField] + 1) % 24}:00`;
      } else if (timeType === 'weekday') {
        const weekdays = [
          '周一',
          '周二',
          '周三',
          '周四',
          '周五',
          '周六',
          '周日',
        ];
        label = weekdays[item[selectField]];
      } else if (timeType === 'month') {
        label = `${item[selectField]}月`;
      }

      return {
        period: item[selectField],
        label,
        count: parseInt(item.count),
        complaintCount: parseInt(item.complaintCount),
        suggestionCount: parseInt(item.suggestionCount),
      };
    });
  }

  /**
   * 获取满意度统计（基于处理结果的简单分析）
   */
  async getSatisfactionStats(
    startDate?: string,
    endDate?: string,
    category?: 'complaint' | 'suggestion',
    subCategory?: 'order' | 'employee' | 'platform' | 'service'
  ) {
    const whereCondition: any = {
      status: { [Op.in]: [ComplaintStatus.RESOLVED, ComplaintStatus.CLOSED] },
      result: { [Op.ne]: null },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')],
      };
    }

    if (category) {
      whereCondition.category = category;
    }

    if (subCategory) {
      whereCondition.subCategory = subCategory;
    }

    const total = await Complaint.count({ where: whereCondition });

    // 简单的满意度分析：基于处理结果中的关键词
    const satisfiedKeywords = ['满意', '解决', '感谢', '好', '及时'];
    const unsatisfiedKeywords = ['不满意', '未解决', '差', '慢', '态度'];

    const complaints = await Complaint.findAll({
      where: whereCondition,
      attributes: ['result', 'category', 'subCategory'],
    });

    let satisfiedCount = 0;
    let unsatisfiedCount = 0;
    let neutralCount = 0;

    complaints.forEach(complaint => {
      const result = complaint.result?.toLowerCase() || '';
      const hasSatisfiedKeyword = satisfiedKeywords.some(keyword =>
        result.includes(keyword)
      );
      const hasUnsatisfiedKeyword = unsatisfiedKeywords.some(keyword =>
        result.includes(keyword)
      );

      if (hasSatisfiedKeyword && !hasUnsatisfiedKeyword) {
        satisfiedCount++;
      } else if (hasUnsatisfiedKeyword && !hasSatisfiedKeyword) {
        unsatisfiedCount++;
      } else {
        neutralCount++;
      }
    });

    return {
      total,
      satisfiedCount,
      unsatisfiedCount,
      neutralCount,
      satisfactionRate:
        total > 0 ? Number(((satisfiedCount / total) * 100).toFixed(1)) : 0,
      unsatisfactionRate:
        total > 0 ? Number(((unsatisfiedCount / total) * 100).toFixed(1)) : 0,
      neutralRate:
        total > 0 ? Number(((neutralCount / total) * 100).toFixed(1)) : 0,
    };
  }
}
