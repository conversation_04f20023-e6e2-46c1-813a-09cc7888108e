import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Order } from './order.entity';
import { Employee } from './employee.entity';

export interface OrderSpecialNoteAttributes {
  /** 特殊情况说明ID */
  id: number;
  /** 关联订单ID */
  orderId: number;
  /** 关联员工ID */
  employeeId: number;
  /** 特殊情况说明内容 */
  content: string;
  /** 特殊情况图片URL数组，最多9张 */
  photos?: string[];
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 关联的订单信息 */
  order?: Order;
  /** 关联的员工信息 */
  employee?: Employee;
}

@Table({ 
  tableName: 'order_special_notes', 
  timestamps: true, 
  comment: '订单特殊情况说明表' 
})
export class OrderSpecialNote
  extends Model<OrderSpecialNoteAttributes>
  implements OrderSpecialNoteAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '特殊情况说明ID',
  })
  id: number;

  @ForeignKey(() => Order)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联订单ID',
  })
  orderId: number;

  @ForeignKey(() => Employee)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联员工ID',
  })
  employeeId: number;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '特殊情况说明内容',
  })
  content: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '特殊情况图片URL数组，最多9张',
  })
  photos?: string[];

  @BelongsTo(() => Order)
  order: Order;

  @BelongsTo(() => Employee)
  employee: Employee;
}
