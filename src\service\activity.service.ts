import { Provide } from '@midwayjs/core';
import { Activity } from '../entity/activity.entity';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Provide()
export class ActivityService extends BaseService<Activity> {
  constructor() {
    super('活动');
  }

  getModel() {
    return Activity;
  }

  async findByTarget(target: string) {
    return await this.findAll({
      query: { target, isPublished: 1 },
    });
  }

  /**
   * 管理端查询活动列表
   */
  async findAllForAdmin(
    current = 1,
    pageSize = 10,
    title?: string,
    target?: string,
    isPublished?: number
  ) {
    const query: any = {};

    if (title) {
      query.title = {
        [Op.like]: `%${title}%`,
      };
    }

    if (target) {
      query.target = target;
    }

    if (isPublished !== undefined) {
      query.isPublished = isPublished;
    }

    return await this.findAll({
      query,
      offset: (current - 1) * pageSize,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });
  }

  /**
   * 发布活动（确保同一时间只有一个活动发布）
   */
  async publishActivity(id: number) {
    const activity = await this.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在');
    }

    if (activity.isPublished === 1) {
      throw new CustomError('活动已经发布');
    }

    // 先取消其他已发布的活动
    await Activity.update(
      {
        isPublished: 0,
        publishedAt: null,
      },
      {
        where: {
          isPublished: 1,
          target: activity.target,
        },
      }
    );

    // 发布当前活动
    await this.update(
      { id },
      {
        isPublished: 1,
        publishedAt: new Date(),
      }
    );

    return true;
  }

  /**
   * 取消发布活动
   */
  async unpublishActivity(id: number) {
    const activity = await this.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在');
    }

    if (activity.isPublished !== 1) {
      throw new CustomError('活动未发布');
    }

    await this.update(
      { id },
      {
        isPublished: 0,
        publishedAt: null,
      }
    );

    return true;
  }

  /**
   * 获取当前发布的活动（小程序端使用）
   */
  async getCurrentPublishedActivity(target = '用户端') {
    const result = await this.findAll({
      query: {
        target,
        isPublished: 1,
      },
      limit: 1,
      order: [['publishedAt', 'DESC']],
    });

    return result.list.length > 0 ? result.list[0] : null;
  }
}
