import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';

export interface AbnormalPriceConfigAttributes {
  /** 配置ID */
  id: number;
  /** 配置键 */
  configKey: string;
  /** 配置值 */
  configValue: string;
  /** 配置描述 */
  description: string;
  /** 配置类型 */
  configType: 'threshold' | 'rule' | 'setting';
  /** 是否启用 */
  isEnabled: boolean;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

@Table({
  tableName: 'abnormal_price_configs',
  comment: '异常价格配置表',
})
export class AbnormalPriceConfig extends Model<AbnormalPriceConfigAttributes> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '配置ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: true,
    comment: '配置键',
  })
  configKey: string;

  @Column({
    type: DataType.STRING(200),
    allowNull: false,
    comment: '配置值',
  })
  configValue: string;

  @Column({
    type: DataType.STRING(200),
    allowNull: false,
    comment: '配置描述',
  })
  description: string;

  @Column({
    type: DataType.ENUM('threshold', 'rule', 'setting'),
    allowNull: false,
    comment: '配置类型',
  })
  configType: 'threshold' | 'rule' | 'setting';

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否启用',
  })
  isEnabled: boolean;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    comment: '更新时间',
  })
  updatedAt: Date;
}
