import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComplaintStatisticsService } from '../service/complaint-statistics.service';
import {
  ComplaintOverviewDto,
  ComplaintTrendDto,
  CustomerComplaintStatisticsDto,
  EmployeeComplaintStatisticsDto,
  ProcessingEfficiencyDto,
  HotIssuesAnalysisDto,
  ComplaintStatusDistributionDto,
} from '../dto/complaint-statistics.dto';

@Controller('/complaint-statistics')
export class ComplaintStatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComplaintStatisticsService;

  /**
   * 获取投诉建议概览统计
   * GET /complaint-statistics/overview
   */
  @Get('/overview', { summary: '获取投诉建议概览统计' })
  async getOverview(@Query() query: ComplaintOverviewDto) {
    const { startDate, endDate } = query;
    return await this.service.getComplaintOverview(startDate, endDate);
  }

  /**
   * 获取投诉建议趋势统计
   * GET /complaint-statistics/trend
   */
  @Get('/trend', { summary: '获取投诉建议趋势统计' })
  async getTrend(@Query() query: ComplaintTrendDto) {
    const { startDate, endDate, periodType = 'day' } = query;

    if (!startDate || !endDate) {
      throw new Error('开始日期和结束日期不能为空');
    }

    return await this.service.getComplaintTrend(startDate, endDate, periodType);
  }

  /**
   * 获取客户投诉建议统计
   * GET /complaint-statistics/customer
   */
  @Get('/customer', { summary: '获取客户投诉建议统计' })
  async getCustomerStatistics(@Query() query: CustomerComplaintStatisticsDto) {
    const {
      startDate,
      endDate,
      page = 1,
      pageSize = 20,
      sortBy = 'totalCount',
      sortOrder = 'desc',
    } = query;

    return await this.service.getCustomerComplaintStatistics(
      startDate,
      endDate,
      page,
      pageSize,
      sortBy,
      sortOrder
    );
  }

  /**
   * 获取员工投诉统计
   * GET /complaint-statistics/employee
   */
  @Get('/employee', { summary: '获取员工投诉统计' })
  async getEmployeeStatistics(@Query() query: EmployeeComplaintStatisticsDto) {
    const {
      startDate,
      endDate,
      page = 1,
      pageSize = 20,
      sortBy = 'complaintCount',
      sortOrder = 'desc',
    } = query;

    return await this.service.getEmployeeComplaintStatistics(
      startDate,
      endDate,
      page,
      pageSize,
      sortBy,
      sortOrder
    );
  }

  /**
   * 获取处理效率统计
   * GET /complaint-statistics/processing-efficiency
   */
  @Get('/processing-efficiency', { summary: '获取处理效率统计' })
  async getProcessingEfficiency(@Query() query: ProcessingEfficiencyDto) {
    const { startDate, endDate } = query;
    return await this.service.getProcessingEfficiencyStats(startDate, endDate);
  }

  /**
   * 获取热点问题分析
   * GET /complaint-statistics/hot-issues
   */
  @Get('/hot-issues', { summary: '获取热点问题分析' })
  async getHotIssues(@Query() query: HotIssuesAnalysisDto) {
    const { startDate, endDate, limit = 10 } = query;
    return await this.service.getHotIssuesAnalysis(startDate, endDate, limit);
  }

  /**
   * 获取投诉建议状态分布
   * GET /complaint-statistics/status-distribution
   */
  @Get('/status-distribution', { summary: '获取投诉建议状态分布' })
  async getStatusDistribution(@Query() query: ComplaintStatusDistributionDto) {
    const { startDate, endDate, category, subCategory } = query;

    // 使用现有的概览统计方法，然后过滤数据
    const overview = await this.service.getComplaintOverview(
      startDate,
      endDate
    );

    const statusStats = overview.statusStats;
    const categoryStats = overview.categoryStats;
    const subCategoryStats = overview.subCategoryStats;

    // 如果有类型过滤，需要重新查询
    if (category || subCategory) {
      // 这里可以扩展服务方法来支持更细粒度的过滤
      // 暂时返回全部数据
    }

    return {
      statusStats,
      categoryStats,
      subCategoryStats,
    };
  }

  /**
   * 获取投诉建议数量排行
   * GET /complaint-statistics/ranking
   */
  @Get('/ranking', { summary: '获取投诉建议数量排行' })
  async getRanking(
    @Query('type') type: 'customer' | 'employee' = 'customer',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit = 10
  ) {
    if (type === 'customer') {
      const result = await this.service.getCustomerComplaintStatistics(
        startDate,
        endDate,
        1,
        limit,
        'totalCount',
        'desc'
      );
      return {
        type: 'customer',
        list: result.list.slice(0, limit),
      };
    } else if (type === 'employee') {
      const result = await this.service.getEmployeeComplaintStatistics(
        startDate,
        endDate,
        1,
        limit,
        'complaintCount',
        'desc'
      );
      return {
        type: 'employee',
        list: result.list.slice(0, limit),
      };
    }

    throw new Error('排行类型必须是customer或employee');
  }

  /**
   * 获取投诉建议处理时效统计
   * GET /complaint-statistics/processing-time
   */
  @Get('/processing-time', { summary: '获取投诉建议处理时效统计' })
  async getProcessingTime(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    const efficiency = await this.service.getProcessingEfficiencyStats(
      startDate,
      endDate
    );

    return {
      averageProcessingHours: efficiency.averageProcessingHours,
      fastProcessingCount: efficiency.fastProcessingCount,
      slowProcessingCount: efficiency.slowProcessingCount,
      fastProcessingRate: efficiency.fastProcessingRate,
      slowProcessingRate: efficiency.slowProcessingRate,
      timeDistribution: {
        '0-24h': efficiency.fastProcessingCount,
        '24-72h': 0, // 需要在服务中计算
        '72h+': efficiency.slowProcessingCount,
      },
    };
  }

  /**
   * 获取投诉建议解决率统计
   * GET /complaint-statistics/resolve-rate
   */
  @Get('/resolve-rate', { summary: '获取投诉建议解决率统计' })
  async getResolveRate(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('groupBy') groupBy: 'category' | 'subCategory' | 'month' = 'category'
  ) {
    const overview = await this.service.getComplaintOverview(
      startDate,
      endDate
    );

    if (groupBy === 'category') {
      return {
        groupBy: 'category',
        data: overview.categoryStats.map(item => ({
          name: item.category,
          count: item.count,
          percentage: item.percentage,
        })),
        totalResolveRate: overview.complaintStats.processRate,
      };
    } else if (groupBy === 'subCategory') {
      return {
        groupBy: 'subCategory',
        data: overview.subCategoryStats.map(item => ({
          name: item.subCategory,
          count: item.count,
          percentage: item.percentage,
        })),
        totalResolveRate: overview.complaintStats.processRate,
      };
    }

    // 按月份统计需要调用趋势接口
    if (groupBy === 'month' && startDate && endDate) {
      const trend = await this.service.getComplaintTrend(
        startDate,
        endDate,
        'month'
      );
      return {
        groupBy: 'month',
        data: trend.map(item => ({
          name: item.period,
          count: item.totalCount,
          resolveRate: item.resolveRate,
        })),
        totalResolveRate: overview.complaintStats.processRate,
      };
    }

    throw new Error('分组类型必须是category、subCategory或month');
  }

  /**
   * 获取处理人员统计
   * GET /complaint-statistics/handler
   */
  @Get('/handler', { summary: '获取处理人员统计' })
  async getHandlerStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 20,
    @Query('sortBy')
    sortBy:
      | 'handledCount'
      | 'avgProcessingHours'
      | 'resolveRate' = 'handledCount',
    @Query('sortOrder') sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    return await this.service.getHandlerStatistics(
      startDate,
      endDate,
      page,
      pageSize,
      sortBy,
      sortOrder
    );
  }

  /**
   * 获取时间分布统计
   * GET /complaint-statistics/time-distribution
   */
  @Get('/time-distribution', { summary: '获取时间分布统计' })
  async getTimeDistribution(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('timeType') timeType: 'hour' | 'weekday' | 'month' = 'hour'
  ) {
    return await this.service.getTimeDistributionStats(
      startDate,
      endDate,
      timeType
    );
  }

  /**
   * 获取满意度统计
   * GET /complaint-statistics/satisfaction
   */
  @Get('/satisfaction', { summary: '获取满意度统计' })
  async getSatisfaction(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('category') category?: 'complaint' | 'suggestion',
    @Query('subCategory')
    subCategory?: 'order' | 'employee' | 'platform' | 'service'
  ) {
    return await this.service.getSatisfactionStats(
      startDate,
      endDate,
      category,
      subCategory
    );
  }
}
