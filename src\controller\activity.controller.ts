import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ActivityService } from '../service/activity.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Controller('/activities')
export class ActivityController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ActivityService;

  @Get('/', { summary: '查询活动列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // title支持模糊查询
    if (queryInfo.title) {
      queryInfo.title = {
        [Op.like]: `%${queryInfo.title}%`,
      };
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
    });
  }

  @Get('/:id', { summary: '按ID查询活动' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定活动');
    }
    return res;
  }

  @Post('/', { summary: '新增活动' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新活动' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除活动' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/target/:target', { summary: '按受众查询活动' })
  async findByTarget(@Param('target') target: string) {
    return await this.service.findByTarget(target);
  }
}
