import {
  Controller,
  Get,
  Post,
  Put,
  Del,
  Param,
  Query,
  Body,
  Inject,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { Context } from '@midwayjs/koa';
import { ActivityService } from '../../service/activity.service';
import { CustomError } from '../../error/custom.error';
import {
  CreateActivityDto,
  UpdateActivityDto,
  QueryActivityDto,
} from '../../dto/activity.dto';

@Controller('/admin/activities')
export class ActivityAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  activityService: ActivityService;

  @Get('/', { summary: '管理端查询活动列表' })
  @Validate()
  async findAll(@Query() queryDto: QueryActivityDto) {
    const { current = 1, pageSize = 10, title, target, isPublished } = queryDto;
    return await this.activityService.findAllForAdmin(
      current,
      pageSize,
      title,
      target,
      isPublished
    );
  }

  @Get('/:id', { summary: '管理端查询活动详情' })
  async findById(@Param('id') id: number) {
    const activity = await this.activityService.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在', 404);
    }
    return activity;
  }

  @Post('/', { summary: '管理端创建活动' })
  @Validate()
  async create(@Body() createActivityDto: CreateActivityDto) {
    this.ctx.logger.info('【管理端创建活动】：', createActivityDto);

    // 验证内容类型与对应内容的一致性
    if (createActivityDto.contentType === 'content') {
      if (
        !createActivityDto.content ||
        createActivityDto.content.trim() === ''
      ) {
        throw new CustomError('选择富文本类型时，富文本内容不能为空');
      }
    } else if (createActivityDto.contentType === 'url') {
      if (!createActivityDto.url || createActivityDto.url.trim() === '') {
        throw new CustomError('选择链接类型时，链接地址不能为空');
      }
      // 简单的URL格式验证
      const urlPattern = /^https?:\/\/.+/;
      if (!urlPattern.test(createActivityDto.url)) {
        throw new CustomError(
          '链接地址格式不正确，必须以http://或https://开头'
        );
      }
    }

    const activity = await this.activityService.create({
      ...createActivityDto,
      isPublished: 0, // 新创建的活动默认未发布
      publishedAt: null,
    });

    return activity;
  }

  @Put('/:id', { summary: '管理端更新活动' })
  @Validate()
  async update(
    @Param('id') id: number,
    @Body() updateActivityDto: UpdateActivityDto
  ) {
    this.ctx.logger.info('【管理端更新活动】：', { id, ...updateActivityDto });

    const activity = await this.activityService.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在', 404);
    }

    // 如果更新了内容类型，验证对应内容的一致性
    if (updateActivityDto.contentType) {
      if (updateActivityDto.contentType === 'content') {
        if (
          !updateActivityDto.content ||
          updateActivityDto.content.trim() === ''
        ) {
          throw new CustomError('选择富文本类型时，富文本内容不能为空');
        }
      } else if (updateActivityDto.contentType === 'url') {
        if (!updateActivityDto.url || updateActivityDto.url.trim() === '') {
          throw new CustomError('选择链接类型时，链接地址不能为空');
        }
        // 简单的URL格式验证
        const urlPattern = /^https?:\/\/.+/;
        if (!urlPattern.test(updateActivityDto.url)) {
          throw new CustomError(
            '链接地址格式不正确，必须以http://或https://开头'
          );
        }
      }
    }

    await this.activityService.update({ id }, updateActivityDto);
    return true;
  }

  @Del('/:id', { summary: '管理端删除活动' })
  async delete(@Param('id') id: number) {
    this.ctx.logger.info('【管理端删除活动】：', { id });

    const activity = await this.activityService.findById(id);
    if (!activity) {
      throw new CustomError('活动不存在', 404);
    }

    // 如果活动已发布，不允许删除
    if (activity.isPublished === 1) {
      throw new CustomError('已发布的活动不能删除，请先取消发布');
    }

    await this.activityService.delete({ id });
    return true;
  }

  @Post('/:id/publish', { summary: '管理端发布活动' })
  async publishActivity(@Param('id') id: number) {
    this.ctx.logger.info('【管理端发布活动】：', { id });

    await this.activityService.publishActivity(id);
    return true;
  }

  @Post('/:id/unpublish', { summary: '管理端取消发布活动' })
  async unpublishActivity(@Param('id') id: number) {
    this.ctx.logger.info('【管理端取消发布活动】：', { id });

    await this.activityService.unpublishActivity(id);
    return true;
  }

  @Get('/published/current', { summary: '管理端查询当前发布的活动' })
  async getCurrentPublished(@Query('target') target = '用户端') {
    return await this.activityService.getCurrentPublishedActivity(target);
  }
}
