# 员工业务规则

## 员工职位权限

### 职位分类
1. **洗护师**:
   - 只能接洗护类订单
   - 不能接美容类订单
   - 职位代码: XI_HU_SHI

2. **美容师**:
   - 可以接洗护类订单
   - 可以接美容类订单
   - 职位代码: MEI_RONG_SHI

### 接单权限控制
- **系统自动过滤**: 根据员工职位自动过滤可接订单
- **权限验证**: 接单时验证员工职位与订单类型匹配
- **错误提示**: 职位不匹配时给出明确提示

## 员工状态管理

### 状态分类
- **在职**: 正常工作状态，可以接单和提供服务
- **离职**: 已离职状态，不能接单但保留历史数据

### 状态变更规则
- **入职**: 创建员工时默认为在职状态
- **离职**: 手动设置离职状态和离职日期
- **数据保留**: 离职员工的历史数据保留，用于统计分析

## 员工信息管理

### 基本信息
- **个人信息**: 姓名、手机号、身份证号
- **工作信息**: 职位、等级、工作经验
- **时间信息**: 入职日期、离职日期

### 证书管理
- **证书类型**: 专业资格证书（非个人身份证件）
- **存储格式**: 数组形式，支持多个证书名称和图片链接
- **更新维护**: 支持添加、删除、修改证书信息

### 个人信息修改权限
- **员工端**: 只能修改昵称和头像
- **管理端**: 可以修改所有信息
- **敏感信息**: 身份证号等敏感信息需要特殊权限

## 出车拍照规则

### 拍照分组
1. **车辆外观**: 展示服务车辆外观
2. **服务人员**: 展示服务人员形象
3. **车内情况**: 展示车内整洁情况

### 拍照要求
- **数量限制**: 每个分组最多9张照片
- **最低要求**: 至少需要上传一个分组的照片
- **图片质量**: 支持图片压缩和尺寸调整

### 打卡记录
- **位置信息**: 记录打卡时的经纬度和地址
- **时间记录**: 自动记录打卡时间
- **描述信息**: 可添加打卡描述和备注

## 位置服务规则

### 位置上报
- **定时上报**: 员工端定时上报当前位置
- **静默处理**: 位置信息异常时静默处理，不影响业务
- **隐私保护**: 只在工作时间记录位置信息

### 位置查询
- **实时位置**: 管理端可查询员工实时位置
- **历史轨迹**: 可查询员工的历史位置轨迹
- **距离计算**: 支持计算员工与客户的距离

## 考勤管理

### 考勤记录
- **上下班打卡**: 记录员工上下班时间
- **工作时长**: 自动计算工作时长
- **异常处理**: 处理忘记打卡等异常情况

### 考勤统计
- **月度统计**: 按月统计员工考勤情况
- **出勤率**: 计算员工出勤率
- **加班统计**: 统计员工加班时间

## 服务质量管理

### 服务评价
- **客户评价**: 客户对员工服务的评价
- **评价统计**: 统计员工的平均评分
- **改进建议**: 根据评价提供改进建议

### 服务时长统计
- **平均时长**: 统计员工各项服务的平均时长
- **效率分析**: 分析员工的服务效率
- **优化建议**: 根据数据提供优化建议

## 钱包管理

### 余额管理
- **收入记录**: 记录员工的服务收入
- **提现功能**: 支持员工提现到银行卡
- **流水记录**: 详细记录收支流水

### 结算规则
- **结算周期**: 按周或按月结算
- **分成比例**: 根据员工等级设置分成比例
- **奖励机制**: 优秀员工额外奖励

## 推广管理

### 推广客户
- **推广关系**: 员工可以推广客户注册
- **推广奖励**: 推广客户消费时获得奖励
- **推广统计**: 统计员工的推广效果

### 奖励机制
- **首次消费**: 推广客户首次消费时的奖励
- **持续奖励**: 推广客户持续消费的奖励
- **等级奖励**: 根据推广数量设置等级奖励

## 权限控制

### 数据权限
- **个人数据**: 员工只能查看和修改自己的数据
- **订单权限**: 只能查看分配给自己的订单
- **客户信息**: 只能查看服务相关的客户信息

### 操作权限
- **接单权限**: 根据职位限制可接订单类型
- **服务权限**: 只能操作自己接的订单
- **修改权限**: 个人信息修改权限限制

## 数据一致性

### 状态同步
- **在线状态**: 实时更新员工在线状态
- **位置同步**: 定期同步员工位置信息
- **订单状态**: 及时同步订单状态变更

### 关联数据
- **订单关联**: 员工离职后保留订单关联关系
- **评价关联**: 保留客户对员工的评价记录
- **收入记录**: 保留员工的收入记录

## 特殊场景处理

### 员工离职
- **订单处理**: 处理离职员工未完成的订单
- **数据保留**: 保留历史数据用于分析
- **权限回收**: 回收离职员工的系统权限

### 职位变更
- **权限更新**: 及时更新职位相关权限
- **订单影响**: 处理职位变更对现有订单的影响
- **培训记录**: 记录职位变更的培训情况

### 异常情况
- **设备故障**: 处理员工设备故障导致的问题
- **网络异常**: 处理网络异常导致的数据同步问题
- **误操作**: 提供误操作的恢复机制

## 性能优化

### 查询优化
- **员工信息缓存**: 缓存常用的员工信息
- **位置查询优化**: 优化位置查询的性能
- **订单查询**: 优化员工订单查询

### 数据库优化
- **索引设计**: 在常用查询字段上建立索引
- **分表策略**: 大数据量表考虑分表
- **读写分离**: 查询和更新操作分离

## 监控告警

### 业务监控
- **接单率**: 监控员工接单率
- **服务质量**: 监控员工服务质量
- **异常行为**: 监控员工异常行为

### 系统监控
- **位置上报**: 监控位置上报是否正常
- **打卡异常**: 监控打卡异常情况
- **数据同步**: 监控数据同步状态
