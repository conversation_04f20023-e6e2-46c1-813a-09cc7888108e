import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 员工资格证书DTO
 */
export class EmployeeCertificateDto {
  @Rule(RuleType.string().max(100).required())
  name: string;

  @Rule(RuleType.string().max(500).required())
  imageUrl: string;
}

/**
 * 员工创建DTO
 */
export class CreateEmployeeDto {
  @Rule(RuleType.string().max(50).required())
  name: string;

  @Rule(RuleType.string().max(20).required())
  phone: string;

  @Rule(RuleType.string().max(50).optional())
  openid?: string;

  @Rule(RuleType.string().max(255).optional())
  avatar?: string;

  @Rule(RuleType.string().max(50).optional())
  position?: string;

  @Rule(RuleType.number().integer().min(1).max(5).optional())
  level?: number;

  @Rule(RuleType.number().integer().min(0).optional())
  workExp?: number;

  @Rule(RuleType.number().min(0).max(5).optional())
  rating?: number;

  @Rule(RuleType.number().min(0).optional())
  walletBalance?: number;

  @Rule(RuleType.number().integer().optional())
  vehicleId?: number;

  @Rule(RuleType.array().items(RuleType.object()).optional())
  certificates?: EmployeeCertificateDto[];

  @Rule(RuleType.date().optional())
  hireDate?: Date;

  @Rule(RuleType.date().optional())
  resignDate?: Date;

  @Rule(RuleType.number().integer().valid(0, 1).optional())
  status?: number;
}

/**
 * 员工更新DTO
 */
export class UpdateEmployeeDto {
  @Rule(RuleType.string().max(50).optional())
  name?: string;

  @Rule(RuleType.string().max(20).optional())
  phone?: string;

  @Rule(RuleType.string().max(50).optional())
  openid?: string;

  @Rule(RuleType.string().max(255).optional())
  avatar?: string;

  @Rule(RuleType.string().max(50).optional())
  position?: string;

  @Rule(RuleType.number().integer().min(1).max(5).optional())
  level?: number;

  @Rule(RuleType.number().integer().min(0).optional())
  workExp?: number;

  @Rule(RuleType.number().min(0).max(5).optional())
  rating?: number;

  @Rule(RuleType.number().min(0).optional())
  walletBalance?: number;

  @Rule(RuleType.number().integer().optional())
  vehicleId?: number;

  @Rule(RuleType.array().items(RuleType.object()).optional())
  certificates?: EmployeeCertificateDto[];

  @Rule(RuleType.date().optional())
  hireDate?: Date;

  @Rule(RuleType.date().optional())
  resignDate?: Date;

  @Rule(RuleType.number().integer().valid(0, 1).optional())
  status?: number;
}
