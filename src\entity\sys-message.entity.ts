import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface SysMessageAttributes {
  /** 消息ID */
  id: number;
  /** 接收用户ID */
  userId: number;
  /** 消息类型：system-系统通知，platform-平台通知，order-订单相关 */
  type: 'system' | 'platform' | 'order';
  /** 消息标题 */
  title: string;
  /** 消息内容 */
  content: string;
  /** 扩展数据（JSON格式） */
  extraData?: string;
  /** 是否已读：0-未读，1-已读 */
  isRead: boolean;
  /** 阅读时间 */
  readAt?: Date;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 删除时间（软删除） */
  deletedAt?: Date;
  /** 关联用户信息 */
  // user?: Customer;
}

@Table({
  tableName: 'sys_messages',
  timestamps: true,
  comment: '消息表',
})
export class SysMessage
  extends Model<SysMessageAttributes>
  implements SysMessageAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '消息ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '接收用户ID',
  })
  userId: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    defaultValue: 'system',
    comment: '消息类型：system-系统通知，platform-平台通知，order-订单相关',
  })
  type: 'system' | 'platform' | 'order';

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '消息标题',
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '消息内容',
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    comment: '扩展数据（JSON格式）',
  })
  extraData?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已读：0-未读，1-已读',
  })
  isRead: boolean;

  @Column({
    type: DataType.DATE,
    comment: '阅读时间',
  })
  readAt?: Date;

  // @BelongsTo(() => Customer, { onDelete: 'CASCADE' })
  // user: Customer;
}
