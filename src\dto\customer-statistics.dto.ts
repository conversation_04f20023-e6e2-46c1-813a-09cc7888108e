import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 用户统计查询DTO
 */
export class CustomerStatisticsQueryDTO {
  @Rule(RuleType.date().optional())
  startDate?: string;

  @Rule(RuleType.date().optional())
  endDate?: string;

  @Rule(RuleType.string().valid('day', 'week', 'month').optional())
  groupBy?: 'day' | 'week' | 'month';

  @Rule(RuleType.number().integer().min(1).optional())
  current?: number;

  @Rule(RuleType.number().integer().min(1).max(100).optional())
  pageSize?: number;
}

/**
 * 用户注册趋势查询DTO
 */
export class CustomerRegistrationTrendDTO extends CustomerStatisticsQueryDTO {
  @Rule(RuleType.string().valid('day', 'week', 'month').optional())
  periodType?: 'day' | 'week' | 'month';
}

/**
 * 用户性别分布查询DTO
 */
export class CustomerGenderDistributionDTO extends CustomerStatisticsQueryDTO {
  @Rule(RuleType.number().integer().valid(0, 1, 2).optional())
  gender?: number;
}

/**
 * 用户会员状态分布查询DTO
 */
export class CustomerMemberStatusDistributionDTO extends CustomerStatisticsQueryDTO {
  @Rule(RuleType.number().integer().valid(0, 1).optional())
  memberStatus?: number;
}
