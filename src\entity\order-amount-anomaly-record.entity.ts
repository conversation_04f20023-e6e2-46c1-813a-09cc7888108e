import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Order } from './order.entity';
import { Employee } from './employee.entity';

/**
 * 订单金额异常类型枚举
 */
export enum OrderAmountAnomalyType {
  /** 原价与实付不匹配 */
  PRICE_MISMATCH = 'price_mismatch',
  /** 优惠金额异常 */
  DISCOUNT_ANOMALY = 'discount_anomaly',
  /** 原价缺失 */
  MISSING_ORIGINAL_PRICE = 'missing_original_price',
  /** 计算错误 */
  CALCULATION_ERROR = 'calculation_error',
  /** 其他异常 */
  OTHER = 'other',
}

/**
 * 异常处理状态枚举
 */
export enum AnomalyProcessStatus {
  /** 待处理 */
  PENDING = 'pending',
  /** 自动修复中 */
  AUTO_FIXING = 'auto_fixing',
  /** 自动修复成功 */
  AUTO_FIXED = 'auto_fixed',
  /** 自动修复失败 */
  AUTO_FIX_FAILED = 'auto_fix_failed',
  /** 需要人工处理 */
  MANUAL_REQUIRED = 'manual_required',
  /** 人工处理中 */
  MANUAL_PROCESSING = 'manual_processing',
  /** 人工处理完成 */
  MANUAL_FIXED = 'manual_fixed',
  /** 已忽略 */
  IGNORED = 'ignored',
}

/**
 * 订单金额异常记录属性接口
 */
export interface OrderAmountAnomalyRecordAttributes {
  /** 异常记录ID */
  id: number;
  /** 关联订单ID */
  orderId: number;
  /** 订单编号（冗余存储） */
  orderSn: string;
  /** 异常类型 */
  anomalyType: OrderAmountAnomalyType;
  /** 处理状态 */
  processStatus: AnomalyProcessStatus;
  /** 异常描述 */
  description: string;
  /** 异常详情（JSON格式存储具体异常信息） */
  anomalyDetails: string;
  /** 原价（异常发现时的值） */
  originalPrice: number;
  /** 实付金额（异常发现时的值） */
  totalFee: number;
  /** 权益卡抵扣（异常发现时的值） */
  cardDeduction: number;
  /** 代金券抵扣（异常发现时的值） */
  couponDeduction: number;
  /** 计算出的正确原价 */
  calculatedOriginalPrice?: number;
  /** 异常金额差值 */
  anomalyAmount: number;
  /** 异常严重程度（1-5，5最严重） */
  severity: number;
  /** 是否可自动修复 */
  canAutoFix: boolean;
  /** 自动修复尝试次数 */
  autoFixAttempts: number;
  /** 最后修复尝试时间 */
  lastFixAttemptAt?: Date;
  /** 修复建议 */
  fixSuggestion?: string;
  /** 处理人员ID */
  handlerId?: number;
  /** 处理人员姓名（冗余存储） */
  handlerName?: string;
  /** 处理时间 */
  handledAt?: Date;
  /** 处理备注 */
  handlerRemark?: string;
  /** 是否已回退 */
  isReverted: boolean;
  /** 回退时间 */
  revertedAt?: Date;
  /** 回退原因 */
  revertReason?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 关联订单信息 */
  order?: Order;
  /** 关联处理人员信息 */
  handler?: Employee;
}

@Table({
  tableName: 'order_amount_anomaly_records',
  timestamps: true,
  comment: '订单金额异常记录表',
})
export class OrderAmountAnomalyRecord
  extends Model<OrderAmountAnomalyRecordAttributes>
  implements OrderAmountAnomalyRecordAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '异常记录ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联订单ID',
  })
  @ForeignKey(() => Order)
  orderId: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '订单编号（冗余存储）',
  })
  orderSn: string;

  @Column({
    type: DataType.ENUM(...Object.values(OrderAmountAnomalyType)),
    allowNull: false,
    comment: '异常类型',
  })
  anomalyType: OrderAmountAnomalyType;

  @Column({
    type: DataType.ENUM(...Object.values(AnomalyProcessStatus)),
    allowNull: false,
    defaultValue: AnomalyProcessStatus.PENDING,
    comment: '处理状态',
  })
  processStatus: AnomalyProcessStatus;

  @Column({
    type: DataType.STRING(500),
    allowNull: false,
    comment: '异常描述',
  })
  description: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '异常详情（JSON格式存储具体异常信息）',
  })
  anomalyDetails: string;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '原价（异常发现时的值）',
  })
  originalPrice: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '实付金额（异常发现时的值）',
  })
  totalFee: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    defaultValue: 0,
    comment: '权益卡抵扣（异常发现时的值）',
  })
  cardDeduction: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    defaultValue: 0,
    comment: '代金券抵扣（异常发现时的值）',
  })
  couponDeduction: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    comment: '计算出的正确原价',
  })
  calculatedOriginalPrice?: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '异常金额差值',
  })
  anomalyAmount: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '异常严重程度（1-5，5最严重）',
  })
  severity: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否可自动修复',
  })
  canAutoFix: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '自动修复尝试次数',
  })
  autoFixAttempts: number;

  @Column({
    type: DataType.DATE,
    comment: '最后修复尝试时间',
  })
  lastFixAttemptAt?: Date;

  @Column({
    type: DataType.STRING(500),
    comment: '修复建议',
  })
  fixSuggestion?: string;

  @Column({
    type: DataType.INTEGER,
    comment: '处理人员ID',
  })
  @ForeignKey(() => Employee)
  handlerId?: number;

  @Column({
    type: DataType.STRING(100),
    comment: '处理人员姓名（冗余存储）',
  })
  handlerName?: string;

  @Column({
    type: DataType.DATE,
    comment: '处理时间',
  })
  handledAt?: Date;

  @Column({
    type: DataType.STRING(500),
    comment: '处理备注',
  })
  handlerRemark?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已回退',
  })
  isReverted: boolean;

  @Column({
    type: DataType.DATE,
    comment: '回退时间',
  })
  revertedAt?: Date;

  @Column({
    type: DataType.STRING(500),
    comment: '回退原因',
  })
  revertReason?: string;

  @BelongsTo(() => Order, { onDelete: 'CASCADE' })
  order?: Order;

  @BelongsTo(() => Employee, { onDelete: 'SET NULL' })
  handler?: Employee;
}
