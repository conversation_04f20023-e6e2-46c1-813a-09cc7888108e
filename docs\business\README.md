# 业务说明文档总览

## 概述

本目录包含宠物服务管理系统的所有业务规则、流程说明、约束条件和特殊场景处理等详细文档。

## 文档结构

### 核心业务模块
- [订单业务规则](./订单业务规则.md) - 订单流程、状态管理、追加服务、服务计时等
- [客户业务规则](./客户业务规则.md) - 客户管理、会员体系、权益卡规则等
- [员工业务规则](./员工业务规则.md) - 员工管理、职位权限、出车拍照等
- [服务业务规则](./服务业务规则.md) - 服务管理、照片墙、活动管理等

### 支持功能模块
- [投诉建议业务规则](./投诉建议业务规则.md) - 投诉建议处理流程和规则
- [支付业务规则](./支付业务规则.md) - 支付流程、退款规则、优惠券等
- [系统业务规则](./系统业务规则.md) - 权限管理、数据一致性等

### 统计分析模块
- [用户数据统计业务规则](./用户数据统计业务规则.md) - 用户统计范围、计算规则、性能优化等
- [收入统计业务规则](./收入统计业务规则.md) - 收入统计范围、计算规则、关键指标、业务应用场景等

## 业务规则分类

### 数据模型规则
- 实体关系定义
- 字段约束条件
- 数据完整性要求
- 级联操作规则

### 业务流程规则
- 状态流转规则
- 操作权限控制
- 业务逻辑约束
- 异常处理机制

### 特殊场景处理
- 边界条件处理
- 异常情况应对
- 数据恢复机制
- 兼容性考虑

## 重要业务概念

### 订单状态流转
```
待付款 → 待接单 → 待服务 → 已出发 → 服务中 → 已完成 → 已评价
                                    ↓
                              已取消/退款中/已退款
```

### 会员体系
- **普通会员**: 注册用户默认状态
- **权益会员**: 购买权益卡后的状态
- **权益卡类型**: 折扣卡、次卡

### 员工职位权限
- **洗护师**: 只能接洗护订单
- **美容师**: 可接洗护和美容订单

### 投诉建议分类
- **大类**: 投诉(complaint) / 建议(suggestion)
- **小类**: 订单(order) / 员工(employee) / 平台(platform) / 服务(service) / 流程(workflow)

## 数据一致性原则

### 删除操作规则
1. **CASCADE删除**: 主要用于强关联数据
2. **SET NULL删除**: 用于操作记录等历史数据
3. **软删除**: 用于重要业务数据

### 冗余字段策略
- 为提高查询性能，适当添加冗余字段
- 避免复杂的JOIN查询
- 保持数据同步更新

### 事务处理
- 复杂操作使用数据库事务
- 外部支付系统调用的异常处理
- 防止重复操作的幂等性设计

## 权限管理原则

### 角色权限
- **管理员**: 最大权限，可操作任何状态的数据
- **员工**: 有限权限，只能操作相关订单
- **用户**: 基础权限，只能操作自己的数据

### 操作权限
- 订单地址修改：管理员任何状态可修改，用户和员工只能在派单前修改
- 订单删除：管理员可删除任何状态，需检查支付信息并退款
- 服务操作：员工只能操作分配给自己的订单

## 业务约束规则

### 时间相关
- 权益卡有效期管理
- 服务时长统计（取最近10次平均值）
- 员工入职离职时间记录

### 数量限制
- 照片上传：每类最多9张
- 投诉图片：最多6张
- 活动管理：同时只能有一个发布状态的活动

### 状态约束
- 0元订单自动跳过支付状态
- 权益卡过期或次数用完自动变回普通会员
- 订单完成需所有服务项目都完成且无未付费追加服务

## 特殊场景处理

### 支付异常
- 微信支付成功但本地更新失败的处理
- 重复退款的防护机制
- 支付超时的订单处理

### 数据删除
- 删除关联文件的同时删除存储文件
- 删除角色时清理相关授权信息
- 删除订单时的退款处理

### 消息推送
- 订单状态变更的微信消息推送
- 订单删除时清除消息订阅
- WebSocket实时消息推送

## 更新日志

- 2024-01-01: 初始业务规则制定
- 文档整理日期: 2025-07-09

## 相关文档

- [API接口文档](../api/README.md) - 对应的API接口定义
- 数据库设计文档 - 数据模型详细说明
- 系统架构文档 - 整体架构设计
