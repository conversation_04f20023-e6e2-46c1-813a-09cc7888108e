# 客户业务规则

## 会员体系规则

### 会员状态管理
- **普通会员**: 用户注册后的默认状态
- **权益会员**: 购买权益卡后的状态
- **状态转换**: 权益卡过期或使用次数用完后自动变回普通会员

### 会员权益
- **普通会员**: 享受基础服务价格
- **权益会员**: 根据权益卡类型享受折扣或次数优惠

## 权益卡规则

### 权益卡类型
1. **折扣卡**:
   - 必须填写折扣率（0-100）
   - 按折扣率计算优惠金额
   - 可设置有效期

2. **次卡**:
   - 必须填写可用次数
   - 折扣率强制为0（按次数使用）
   - 每次使用扣减一次

### 权益卡约束
- **有效期**: 非必填字段，不填表示永久有效
- **使用限制**: 次卡用完或过期后自动失效
- **叠加使用**: 同一订单只能使用一张权益卡

### 权益卡购买
- **购买时间**: 前端不提供时使用当前时间作为默认值
- **支付方式**: 支持微信支付
- **生效时间**: 支付成功后立即生效

## 代金券规则

### 使用条件
- **满减条件**: 订单金额满足最低使用金额
- **服务限制**: 可限制特定服务类型使用
- **有效期**: 必须在有效期内使用

### 使用优先级
1. 先使用即将过期的代金券
2. 优先使用金额较大的代金券
3. 同一订单只能使用一张代金券

### 代金券购买
- **购买时间**: 前端不提供时使用当前时间作为默认值
- **支付方式**: 支持微信支付
- **生效时间**: 支付成功后立即生效

## 客户推广规则

### 推广关系
- **一对多关系**: 一个员工可以推广多个客户
- **唯一性**: 每个客户只能被一个员工推广
- **设置时机**: 客户注册时或后续设置

### 推广员权益
- **推广奖励**: 推广的客户消费时获得奖励
- **统计查询**: 可查询推广的客户列表和消费情况

## 宠物管理规则

### 宠物信息
- **基本信息**: 姓名、类型、品种、年龄、体重、性别
- **关联订单**: 每个服务项目必须关联一个宠物
- **信息更新**: 客户可随时更新宠物信息

### 宠物约束
- **数量限制**: 每个客户可添加多个宠物
- **删除限制**: 有关联订单的宠物不能删除
- **信息完整性**: 创建订单时必须选择宠物

## 地址管理规则

### 地址信息
- **基本信息**: 地址、经纬度、详细地址、备注
- **默认地址**: 每个客户只能有一个默认地址
- **位置服务**: 支持地址和坐标的转换

### 地址约束
- **数量限制**: 每个客户可添加多个地址
- **默认设置**: 设置新默认地址时自动取消原默认地址
- **删除限制**: 有关联订单的地址不能删除

## 客户信息管理

### 个人信息修改
- **允许修改**: 昵称、头像
- **不允许修改**: 手机号（用作登录凭证）
- **修改权限**: 客户只能修改自己的信息

### 隐私保护
- **信息脱敏**: 对外展示时手机号部分隐藏
- **权限控制**: 员工只能查看服务相关的客户信息
- **数据安全**: 敏感信息加密存储

## 积分规则

### 积分获取
- **消费积分**: 每消费1元获得1积分
- **推广积分**: 推广新客户获得奖励积分
- **活动积分**: 参与活动获得额外积分

### 积分使用
- **抵扣现金**: 100积分=1元
- **兑换优惠券**: 积分兑换代金券
- **使用限制**: 单次订单积分抵扣不超过订单金额的50%

## 数据一致性规则

### 会员状态同步
- **权益卡购买**: 购买成功后立即更新会员状态
- **权益卡过期**: 定时任务检查并更新过期用户状态
- **次卡用完**: 使用完毕后立即更新会员状态

### 关联数据处理
- **客户删除**: 软删除，保留历史订单关联
- **宠物删除**: 检查是否有关联订单
- **地址删除**: 检查是否有关联订单

## 特殊场景处理

### 权益卡异常
- **支付成功但未生效**: 提供手动激活功能
- **重复购买**: 允许购买多张，按购买顺序使用
- **退款处理**: 未使用的权益卡支持退款

### 代金券异常
- **过期处理**: 过期代金券自动失效
- **误用处理**: 提供撤销使用功能（限时）
- **系统赠送**: 支持系统批量赠送代金券

### 推广关系异常
- **员工离职**: 推广关系保持不变，奖励停止
- **重复设置**: 后设置的推广关系覆盖前面的
- **数据修复**: 提供推广关系修复工具

## 业务监控

### 关键指标
- **会员转化率**: 普通会员转权益会员的比例
- **权益卡使用率**: 权益卡的使用情况
- **客户活跃度**: 客户的消费频次和金额

### 异常监控
- **异常注册**: 监控批量注册等异常行为
- **优惠券滥用**: 监控优惠券的异常使用
- **数据异常**: 监控会员状态等数据异常

## 性能优化

### 查询优化
- **会员信息缓存**: 缓存常用的会员信息
- **权益卡查询**: 优化可用权益卡的查询
- **推广关系**: 缓存推广关系数据

### 数据库优化
- **索引设计**: 在常用查询字段上建立索引
- **分页查询**: 大数据量查询使用分页
- **读写分离**: 查询和更新操作分离
