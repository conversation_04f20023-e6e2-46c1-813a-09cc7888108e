import {
  Controller,
  Get,
  Post,
  Del,
  Param,
  Query,
  Body,
  Inject,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate } from '@midwayjs/validate';
import { MessageService } from '../service/sys-message.service';
import {
  GetMessageListDTO,
  CreateMessageDTO,
  CreateMessageFromTemplateDTO,
} from '../dto/message.dto';
import { CustomError } from '../error/custom.error';
import { TokenPayload } from '../interface';

@Controller('/api/message')
export class MessageController {
  @Inject()
  ctx: Context;

  @Inject()
  messageService: MessageService;

  @Get('/list/:userId')
  @Validate()
  async getMessageList(
    @Param('userId') userId: number,
    @Query() query: GetMessageListDTO
  ) {
    // 验证用户权限：只能查看自己的消息
    const currentUser = this.ctx.state.user as TokenPayload;
    if (currentUser.userId !== userId) {
      throw new CustomError('无权限查看其他用户的消息');
    }

    const { type, page = 1, limit = 20 } = query;
    return await this.messageService.getUserMessages(userId, type, page, limit);
  }

  @Get('/detail/:messageId')
  async getMessageDetail(@Param('messageId') messageId: number) {
    return await this.messageService.getMessageDetail(messageId);
  }

  @Post('/mark-read/:messageId')
  async markAsRead(@Param('messageId') messageId: number) {
    return await this.messageService.markAsRead(messageId);
  }

  @Post('/mark-all-read/:userId')
  async markAllAsRead(@Param('userId') userId: number) {
    // 验证用户权限：只能操作自己的消息
    const currentUser = this.ctx.state.user as TokenPayload;
    if (currentUser.userId !== userId) {
      throw new CustomError('无权限操作其他用户的消息');
    }

    return await this.messageService.markAllAsRead(userId);
  }

  @Del('/delete/:messageId')
  async deleteMessage(@Param('messageId') messageId: number) {
    return await this.messageService.deleteMessage(messageId);
  }

  @Get('/unread-count/:userId')
  async getUnreadCount(@Param('userId') userId: number) {
    // 验证用户权限：只能查看自己的未读消息数量
    const currentUser = this.ctx.state.user as TokenPayload;
    if (currentUser.userId !== userId) {
      throw new CustomError('无权限查看其他用户的消息');
    }

    return await this.messageService.getUnreadCount(userId);
  }

  @Post('/create')
  @Validate()
  async createMessage(@Body() body: CreateMessageDTO) {
    return await this.messageService.createMessage(body);
  }

  @Post('/create-from-template')
  @Validate()
  async createMessageFromTemplate(@Body() body: CreateMessageFromTemplateDTO) {
    return await this.messageService.createMessageFromTemplate(
      body.templateCode,
      body.userId,
      body.variables
    );
  }
}
