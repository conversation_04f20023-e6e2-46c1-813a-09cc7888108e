# 宠物API接口

## 1. 查询接口

### 1.1 获取指定客户的宠物数量
- **接口**: `GET /pets/customer/{customerId}/count`
- **描述**: 获取指定客户的宠物数量
- **参数**: 
  - `customerId` (number): 客户ID
- **返回数据**:
  ```json
  {
    "errCode": 0,
    "msg": "success",
    "data": {
      "customerId": 123,
      "petCount": 2
    }
  }
  ```

## 错误码说明

- `400`: 客户ID不能为空或格式错误
- `404`: 客户不存在

## 业务规则说明

### 权限规则
- 该接口为公开接口，不需要特殊权限验证
- 只返回宠物数量，不返回具体宠物信息

### 数据规则
- 只统计属于指定客户的宠物数量
- 包含所有状态的宠物（如果有软删除功能的话）
- 返回的数量为非负整数

## 相关接口

- `GET /customers/{id}/pets` - 获取客户的宠物列表（在客户接口中）
- `POST /customers/{id}/pet` - 新增宠物（在客户接口中）
- `PUT /customers/{id}/pet/{petId}` - 更新宠物（在客户接口中）
- `DELETE /customers/{id}/pet/{petId}` - 删除宠物（在客户接口中）
