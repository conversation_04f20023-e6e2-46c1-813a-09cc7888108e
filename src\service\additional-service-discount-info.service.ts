import { Provide, Inject } from '@midwayjs/core';
import { AdditionalServiceDiscountInfo } from '../entity/additional-service-discount-info.entity';
import { DiscountType } from '../entity/order-discount-info.entity';
import { BaseService } from '../common/BaseService';
import { CustomerCouponService } from './customer-coupon.service';
import { CustomerMembershipCardService } from './customer-membership-card.service';
import { CouponUsageRecordService } from './coupon-usage-record.service';
import { MembershipCardUsageRecordService } from './membership-card-usage-record.service';

@Provide()
export class AdditionalServiceDiscountInfoService extends BaseService<AdditionalServiceDiscountInfo> {
  @Inject()
  customerCouponService: CustomerCouponService;

  @Inject()
  customerMembershipCardService: CustomerMembershipCardService;

  @Inject()
  couponUsageRecordService: CouponUsageRecordService;

  @Inject()
  membershipCardUsageRecordService: MembershipCardUsageRecordService;

  constructor() {
    super('追加服务优惠信息');
  }

  getModel() {
    return AdditionalServiceDiscountInfo;
  }

  /**
   * 创建追加服务订单优惠信息并处理卡券使用
   * @param additionalServiceOrderId 追加服务订单ID
   * @param discountInfos 优惠信息列表
   */
  async createAdditionalServiceDiscountInfos(
    additionalServiceOrderId: number,
    discountInfos: Array<{
      discountType: DiscountType;
      discountId: number;
      discountAmount: number;
    }>
  ) {
    if (!discountInfos || discountInfos.length === 0) {
      return [];
    }

    const results = [];

    for (const info of discountInfos) {
      try {
        // 创建追加服务优惠信息记录
        const discountInfo = await this.create({
          additionalServiceOrderId,
          discountType: info.discountType,
          discountId: info.discountId,
          discountAmount: info.discountAmount,
          isRefunded: false,
        });

        // 根据优惠类型使用相应的卡券
        if (info.discountType === DiscountType.COUPON) {
          // 使用代金券 - 传递isAdditionalService=true，避免创建使用记录
          await this.customerCouponService.useCoupon(
            info.discountId,
            additionalServiceOrderId,
            true // 标记为追加服务，不创建使用记录
          );
        } else if (info.discountType === DiscountType.MEMBERSHIP_CARD) {
          // 使用权益卡 - 传递isAdditionalService=true，避免创建使用记录
          await this.customerMembershipCardService.useCard(
            info.discountId,
            additionalServiceOrderId,
            true // 标记为追加服务，不创建使用记录
          );
        }

        results.push(discountInfo);
      } catch (error) {
        console.error('[追加服务优惠] 处理优惠信息失败：', error);
        throw error;
      }
    }

    return results;
  }

  /**
   * 退回追加服务订单使用的卡券
   * @param additionalServiceOrderId 追加服务订单ID
   * @param reason 退回原因
   */
  async refundAdditionalServiceDiscounts(
    additionalServiceOrderId: number,
    reason: string
  ) {
    // 查找追加服务订单的所有优惠信息
    const discountInfos = await this.findAll({
      query: { additionalServiceOrderId, isRefunded: false },
    });

    if (
      !discountInfos ||
      !discountInfos.list ||
      discountInfos.list.length === 0
    ) {
      return { refundedCount: 0 };
    }

    let refundedCount = 0;

    for (const info of discountInfos.list) {
      try {
        // 根据优惠类型退回相应的卡券
        if (info.discountType === DiscountType.COUPON) {
          // 退回代金券 - 只增加次数，不处理使用记录（因为没有创建）
          await this.customerCouponService.addTimes(info.discountId, 1);
        } else if (info.discountType === DiscountType.MEMBERSHIP_CARD) {
          // 退回权益卡 - 只增加次数，不处理使用记录（因为没有创建）
          await this.customerMembershipCardService.addTimes(info.discountId, 1);
        }

        // 更新优惠信息为已退回
        await info.update({
          isRefunded: true,
          refundTime: new Date(),
          refundDescription: reason,
        });

        refundedCount++;
      } catch (error) {
        console.error('[追加服务退款] 处理优惠信息失败：', error);
        throw error;
      }
    }

    return { refundedCount };
  }

  /**
   * 检查追加服务订单是否已退回卡券
   * @param additionalServiceOrderId 追加服务订单ID
   */
  async checkAdditionalServiceRefundStatus(additionalServiceOrderId: number) {
    const discountInfos = await this.findAll({
      query: { additionalServiceOrderId },
    });

    if (
      !discountInfos ||
      !discountInfos.list ||
      discountInfos.list.length === 0
    ) {
      return {
        hasDiscountInfos: false,
        allRefunded: false,
        infoCount: 0,
      };
    }

    // 检查是否所有优惠信息都已退回
    const allRefunded = discountInfos.list.every(
      (info: AdditionalServiceDiscountInfo) => info.isRefunded
    );

    return {
      hasDiscountInfos: true,
      allRefunded,
      infoCount: discountInfos.list.length,
    };
  }

  /**
   * 获取追加服务订单的优惠信息
   * @param additionalServiceOrderId 追加服务订单ID
   */
  async getAdditionalServiceDiscountInfos(additionalServiceOrderId: number) {
    return await this.findAll({
      query: { additionalServiceOrderId },
    });
  }

  /**
   * 处理追加服务订单优惠信息
   * @param additionalServiceOrderId 追加服务订单ID
   * @param discountInfos 优惠信息列表
   */
  async processAdditionalServiceDiscounts(
    additionalServiceOrderId: number,
    discountInfos: Array<{
      discountType: DiscountType;
      discountId: number;
      discountAmount: number;
    }>
  ) {
    // 调用创建追加服务优惠信息方法，同时处理卡券使用
    return await this.createAdditionalServiceDiscountInfos(
      additionalServiceOrderId,
      discountInfos
    );
  }
}
