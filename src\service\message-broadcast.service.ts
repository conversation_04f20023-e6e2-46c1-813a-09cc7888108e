import { Provide, Inject, Logger, Scope, ScopeEnum } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { WebSocketService } from './websocket.service';
import { Order, OrderDetail, Service, ServiceType } from '../entity';

/**
 * 消息广播服务
 * 统一处理各种业务消息的广播
 */
@Provide()
@Scope(ScopeEnum.Singleton)
export class MessageBroadcastService {
  @Inject()
  webSocketService: WebSocketService;

  @Logger()
  logger: ILogger;

  /**
   * 广播新订单消息
   * @param order 订单对象
   */
  async broadcastNewOrder(order: Order) {
    try {
      if (!order) {
        this.logger.error('广播新订单消息失败：订单对象为空');
        return;
      }

      // 如果订单对象没有包含orderDetails，则重新查询
      let orderWithDetails = order;
      if (!order.orderDetails) {
        const fullOrder = await Order.findByPk(order.id, {
          include: [
            {
              model: OrderDetail,
              include: [
                {
                  model: Service,
                  include: [
                    {
                      model: ServiceType,
                      attributes: ['id', 'type', 'name'],
                    },
                  ],
                  attributes: ['id', 'serviceName'],
                },
              ],
              attributes: ['id', 'serviceId'],
            },
          ],
          attributes: ['id', 'sn'],
        });

        if (!fullOrder) {
          this.logger.error(
            `广播新订单消息失败：订单不存在，订单ID ${order.id}`
          );
          return;
        }
        orderWithDetails = fullOrder;
      }

      // 提取订单中的服务类型
      const serviceTypes =
        orderWithDetails.orderDetails?.map(detail => ({
          serviceType: detail.service?.serviceType?.type,
          serviceTypeName: detail.service?.serviceType?.name,
          serviceName: detail.service?.serviceName,
        })) || [];

      if (!serviceTypes.length) {
        this.logger.error(
          `广播新订单消息失败：订单中没有服务类型，订单ID ${orderWithDetails.id}`
        );
        return;
      }

      const serviceType = serviceTypes[0];

      const message = {
        type: 'new_order',
        data: {
          orderId: orderWithDetails.id,
          orderSn: orderWithDetails.sn,
          serviceType, // 包含服务类型信息
        },
        timestamp: Date.now(),
      };

      await this.webSocketService.broadcastToAll(message);
      this.logger.info(
        `成功广播新订单消息：订单ID ${
          orderWithDetails.id
        }，服务类型 ${serviceTypes.map(st => st.serviceType).join(', ')}`
      );
    } catch (error) {
      this.logger.error(`广播新订单消息失败：订单ID ${order.id}`, error);
    }
  }

  /**
   * 广播取消订单消息
   * @param orderId 订单ID
   */
  async broadcastCancelOrder(orderId: number) {
    try {
      const message = {
        type: 'cancel_order',
        data: {
          orderId,
        },
        timestamp: Date.now(),
      };

      await this.webSocketService.broadcastToAll(message);
      this.logger.info(`成功广播取消订单消息：订单ID ${orderId}`);
    } catch (error) {
      this.logger.error(`广播取消订单消息失败：订单ID ${orderId}`, error);
    }
  }

  /**
   * 广播订单状态变更消息
   * @param orderId 订单ID
   * @param status 新状态
   */
  async broadcastOrderStatusChange(orderId: number, status: string) {
    try {
      const message = {
        type: 'order_status_change',
        data: {
          orderId,
          status,
        },
        timestamp: Date.now(),
      };

      await this.webSocketService.broadcastToAll(message);
      this.logger.info(
        `成功广播订单状态变更消息：订单ID ${orderId}, 状态 ${status}`
      );
    } catch (error) {
      this.logger.error(`广播订单状态变更消息失败：订单ID ${orderId}`, error);
    }
  }

  /**
   * 广播系统通知消息
   * @param message 通知内容
   * @param level 通知级别
   */
  async broadcastSystemNotification(
    message: string,
    level: 'info' | 'warning' | 'error' = 'info'
  ) {
    try {
      const notification = {
        type: 'system_notification',
        data: {
          message,
          level,
        },
        timestamp: Date.now(),
      };

      await this.webSocketService.broadcastToAll(notification);
      this.logger.info(`成功广播系统通知：${message}`);
    } catch (error) {
      this.logger.error(`广播系统通知失败：${message}`, error);
    }
  }
}
