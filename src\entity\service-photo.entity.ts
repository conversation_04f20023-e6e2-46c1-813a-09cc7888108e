import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Order } from './order.entity';
import { Employee } from './employee.entity';

export interface ServicePhotoAttributes {
  /** 服务照片ID */
  id: number;
  /** 关联订单ID */
  orderId: number;
  /** 关联员工ID */
  employeeId: number;
  /** 服务前照片链接数组，最多9张 */
  beforePhotos?: string[];
  /** 服务前照片上传时间 */
  beforePhotoTime?: Date;
  /** 服务后照片链接数组，最多9张 */
  afterPhotos?: string[];
  /** 服务后照片上传时间 */
  afterPhotoTime?: Date;
  /** 关联的订单信息 */
  order?: Order;
  /** 关联的员工信息 */
  employee?: Employee;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({ tableName: 'service_photos', timestamps: true, comment: '服务照片表' })
export class ServicePhoto
  extends Model<ServicePhotoAttributes>
  implements ServicePhotoAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '服务照片ID',
  })
  id: number;

  @ForeignKey(() => Order)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联订单ID',
  })
  orderId: number;

  @ForeignKey(() => Employee)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联员工ID',
  })
  employeeId: number;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '服务前照片链接数组，最多9张',
  })
  beforePhotos?: string[];

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '服务前照片上传时间',
  })
  beforePhotoTime?: Date;

  @Column({
    type: DataType.JSON,
    allowNull: true,
    comment: '服务后照片链接数组，最多9张',
  })
  afterPhotos?: string[];

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '服务后照片上传时间',
  })
  afterPhotoTime?: Date;

  @BelongsTo(() => Order)
  order: Order;

  @BelongsTo(() => Employee)
  employee: Employee;
}
