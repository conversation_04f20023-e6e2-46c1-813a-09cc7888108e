# 用户数据统计接口

## 1. 用户概览统计

### 接口信息
- **路径**: `GET /customer-statistics/overview`
- **描述**: 获取用户概览统计数据，包括总人数、性别分布、会员状态分布等
- **权限**: 管理端接口，需要登录认证

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

### 请求示例
```
GET /customer-statistics/overview
GET /customer-statistics/overview?startDate=2024-01-01&endDate=2024-12-31
```

### 响应数据
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalUsers": 698,
    "todayNewUsers": 5,
    "monthNewUsers": 120,
    "genderDistribution": [
      {
        "gender": 0,
        "genderName": "女",
        "count": 372,
        "percentage": 53.3
      },
      {
        "gender": 1,
        "genderName": "男",
        "count": 112,
        "percentage": 16.05
      },
      {
        "gender": 2,
        "genderName": "未知",
        "count": 214,
        "percentage": 30.66
      }
    ],
    "memberStatusDistribution": [
      {
        "memberStatus": 0,
        "statusName": "普通会员",
        "count": 695,
        "percentage": 99.57
      },
      {
        "memberStatus": 1,
        "statusName": "权益会员",
        "count": 3,
        "percentage": 0.43
      }
    ]
  }
}
```

### 响应字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| totalUsers | number | 总用户数（启用状态） |
| todayNewUsers | number | 今日新增用户数 |
| monthNewUsers | number | 本月新增用户数 |
| genderDistribution | array | 性别分布统计 |
| memberStatusDistribution | array | 会员状态分布统计 |

#### 性别分布字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| gender | number | 性别：0-女，1-男，2-未知 |
| genderName | string | 性别名称 |
| count | number | 该性别用户数量 |
| percentage | number | 占总用户的百分比 |

#### 会员状态分布字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| memberStatus | number | 会员状态：0-普通会员，1-权益会员 |
| statusName | string | 会员状态名称 |
| count | number | 该状态用户数量 |
| percentage | number | 占总用户的百分比 |

---

## 2. 用户注册趋势统计

### 接口信息
- **路径**: `GET /customer-statistics/registration-trend`
- **描述**: 获取指定时间段的用户注册趋势数据，支持按日/周/月统计
- **权限**: 管理端接口，需要登录认证

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 是 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 是 | 结束日期，格式：YYYY-MM-DD |
| periodType | string | 否 | 统计周期：day/week/month，默认day |

### 请求示例
```
GET /customer-statistics/registration-trend?startDate=2024-01-01&endDate=2024-12-31&periodType=month
```

### 响应数据
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "period": "2024-01",
      "count": 45
    },
    {
      "period": "2024-02",
      "count": 38
    },
    {
      "period": "2024-03",
      "count": 52
    }
  ]
}
```

### 响应字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| period | string | 时间周期（格式根据periodType变化） |
| count | number | 该周期内注册用户数 |

---

## 3. 用户性别分布统计

### 接口信息
- **路径**: `GET /customer-statistics/gender-distribution`
- **描述**: 获取用户性别分布统计数据
- **权限**: 管理端接口，需要登录认证

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

### 请求示例
```
GET /customer-statistics/gender-distribution
```

### 响应数据
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "gender": 0,
      "genderName": "女",
      "count": 372,
      "percentage": 53.3
    },
    {
      "gender": 1,
      "genderName": "男",
      "count": 112,
      "percentage": 16.05
    }
  ]
}
```

---

## 4. 用户会员状态分布统计

### 接口信息
- **路径**: `GET /customer-statistics/member-status-distribution`
- **描述**: 获取用户会员状态分布统计数据
- **权限**: 管理端接口，需要登录认证

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

### 请求示例
```
GET /customer-statistics/member-status-distribution
```

### 响应数据
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "memberStatus": 0,
      "statusName": "普通会员",
      "count": 695,
      "percentage": 99.57
    },
    {
      "memberStatus": 1,
      "statusName": "权益会员",
      "count": 3,
      "percentage": 0.43
    }
  ]
}
```

---

## 5. 用户统计汇总

### 接口信息
- **路径**: `GET /customer-statistics/summary`
- **描述**: 一次性获取用户概览、性别分布、会员状态分布等汇总数据
- **权限**: 管理端接口，需要登录认证

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

### 请求示例
```
GET /customer-statistics/summary
```

### 响应数据
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "overview": {
      "totalUsers": 698,
      "todayNewUsers": 5,
      "monthNewUsers": 120
    },
    "genderDistribution": [
      {
        "gender": 0,
        "genderName": "女",
        "count": 372,
        "percentage": 53.3
      }
    ],
    "memberStatusDistribution": [
      {
        "memberStatus": 0,
        "statusName": "普通会员",
        "count": 695,
        "percentage": 99.57
      }
    ]
  }
}
```

---

## 6. 用户增长趋势（最近30天）

### 接口信息
- **路径**: `GET /customer-statistics/growth-trend`
- **描述**: 获取最近30天的用户增长趋势数据
- **权限**: 管理端接口，需要登录认证

### 请求参数
无

### 请求示例
```
GET /customer-statistics/growth-trend
```

### 响应数据
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "period": "最近30天",
    "data": [
      {
        "period": "2024-06-10",
        "count": 3
      },
      {
        "period": "2024-06-11",
        "count": 5
      }
    ]
  }
}
```

### 注意事项
1. 所有统计只包含启用状态（status=1）的用户
2. 性别字段：0-女，1-男，2-未知
3. 会员状态：0-普通会员，1-权益会员
4. 百分比保留两位小数
5. 时间范围过滤基于用户创建时间（createdAt）
6. 趋势统计支持按日、周、月三种周期类型
