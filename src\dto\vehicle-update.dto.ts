import { Rule, RuleType } from '@midwayjs/validate';

export class VehicleUpdateDto {
  @Rule(RuleType.number().optional().min(0).max(999999))
  mileage?: number;

  @Rule(RuleType.string().optional().max(1000))
  appearance?: string;

  @Rule(RuleType.date().optional())
  insuranceExpiry?: Date;

  @Rule(RuleType.date().optional())
  licenseExpiry?: Date;

  @Rule(RuleType.string().optional().max(2000))
  supplies?: string;
}
