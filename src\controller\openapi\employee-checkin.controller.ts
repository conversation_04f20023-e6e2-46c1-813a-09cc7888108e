import {
  Controller,
  Post,
  Get,
  Del,
  Body,
  Param,
  Query,
  Inject,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { EmployeeCheckInService } from '../../service/employee-checkin.service';
import { EmployeeCheckInDto } from '../../dto/employee-checkin.dto';
import { CustomError } from '../../error/custom.error';

@Controller('/openapi/employee-checkins')
export class OpenapiEmployeeCheckInController {
  @Inject()
  employeeCheckInService: EmployeeCheckInService;

  @Post('/', { summary: '员工出车拍照打卡' })
  @Validate()
  async checkIn(@Body() body: EmployeeCheckInDto) {
    return await this.employeeCheckInService.checkIn(body);
  }

  @Get('/employee/:employeeId', { summary: '查询员工打卡记录列表' })
  async findByEmployee(
    @Param('employeeId') employeeId: number,
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    return await this.employeeCheckInService.findByEmployeeId(
      employeeId,
      current,
      pageSize,
      startDate,
      endDate
    );
  }

  @Get('/:id', { summary: '查询打卡记录详情' })
  async findById(@Param('id') id: number) {
    const checkIn = await this.employeeCheckInService.findOne({
      where: { id },
      include: ['employee'],
    });
    if (!checkIn) {
      throw new CustomError('打卡记录不存在', 404);
    }
    return checkIn;
  }

  @Del('/:id/employee/:employeeId', { summary: '员工删除自己的打卡记录' })
  async deleteByEmployee(
    @Param('id') id: number,
    @Param('employeeId') employeeId: number
  ) {
    return await this.employeeCheckInService.deleteCheckIn(id, employeeId);
  }

  @Get('/employee/:employeeId/statistics', { summary: '查询员工打卡统计' })
  async getEmployeeStatistics(
    @Param('employeeId') employeeId: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    return await this.employeeCheckInService.getStatistics(
      employeeId,
      startDate,
      endDate
    );
  }

  @Get('/employee/:employeeId/today', { summary: '查询员工今日打卡记录' })
  async getTodayCheckIns(@Param('employeeId') employeeId: number) {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    return await this.employeeCheckInService.findByEmployeeId(
      employeeId,
      1,
      50, // 一天最多50次打卡应该够了
      todayStr,
      todayStr
    );
  }
}
