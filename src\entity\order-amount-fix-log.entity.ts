import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Order } from './order.entity';
import { OrderAmountAnomalyRecord } from './order-amount-anomaly-record.entity';
import { Employee } from './employee.entity';

/**
 * 修复操作类型枚举
 */
export enum FixOperationType {
  /** 自动修复 */
  AUTO_FIX = 'auto_fix',
  /** 手动修复 */
  MANUAL_FIX = 'manual_fix',
  /** 批量修复 */
  BATCH_FIX = 'batch_fix',
  /** 回退操作 */
  REVERT = 'revert',
}

/**
 * 修复结果枚举
 */
export enum FixResult {
  /** 成功 */
  SUCCESS = 'success',
  /** 失败 */
  FAILED = 'failed',
  /** 部分成功 */
  PARTIAL_SUCCESS = 'partial_success',
  /** 跳过 */
  SKIPPED = 'skipped',
}

/**
 * 订单金额修复日志属性接口
 */
export interface OrderAmountFixLogAttributes {
  /** 修复日志ID */
  id: number;
  /** 关联异常记录ID */
  anomalyRecordId: number;
  /** 关联订单ID */
  orderId: number;
  /** 订单编号（冗余存储） */
  orderSn: string;
  /** 修复操作类型 */
  operationType: FixOperationType;
  /** 修复结果 */
  result: FixResult;
  /** 修复前数据（JSON格式） */
  beforeData: string;
  /** 修复后数据（JSON格式） */
  afterData: string;
  /** 修复详情描述 */
  description: string;
  /** 修复执行的SQL或操作步骤 */
  executedOperations?: string;
  /** 修复耗时（毫秒） */
  executionTime?: number;
  /** 错误信息（修复失败时记录） */
  errorMessage?: string;
  /** 错误堆栈（修复失败时记录） */
  errorStack?: string;
  /** 操作人员ID */
  operatorId?: number;
  /** 操作人员姓名（冗余存储） */
  operatorName?: string;
  /** 操作人员类型 */
  operatorType: 'system' | 'admin' | 'employee';
  /** 是否可回退 */
  canRevert: boolean;
  /** 回退数据（JSON格式，用于回退操作） */
  revertData?: string;
  /** 是否已回退 */
  isReverted: boolean;
  /** 回退时间 */
  revertedAt?: Date;
  /** 回退操作人ID */
  revertOperatorId?: number;
  /** 回退操作人姓名 */
  revertOperatorName?: string;
  /** 回退原因 */
  revertReason?: string;
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 关联异常记录 */
  anomalyRecord?: OrderAmountAnomalyRecord;
  /** 关联订单信息 */
  order?: Order;
  /** 关联操作人员信息 */
  operator?: Employee;
  /** 关联回退操作人员信息 */
  revertOperator?: Employee;
}

@Table({
  tableName: 'order_amount_fix_logs',
  timestamps: true,
  comment: '订单金额修复日志表',
})
export class OrderAmountFixLog
  extends Model<OrderAmountFixLogAttributes>
  implements OrderAmountFixLogAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '修复日志ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联异常记录ID',
  })
  @ForeignKey(() => OrderAmountAnomalyRecord)
  anomalyRecordId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联订单ID',
  })
  @ForeignKey(() => Order)
  orderId: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '订单编号（冗余存储）',
  })
  orderSn: string;

  @Column({
    type: DataType.ENUM(...Object.values(FixOperationType)),
    allowNull: false,
    comment: '修复操作类型',
  })
  operationType: FixOperationType;

  @Column({
    type: DataType.ENUM(...Object.values(FixResult)),
    allowNull: false,
    comment: '修复结果',
  })
  result: FixResult;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '修复前数据（JSON格式）',
  })
  beforeData: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '修复后数据（JSON格式）',
  })
  afterData: string;

  @Column({
    type: DataType.STRING(500),
    allowNull: false,
    comment: '修复详情描述',
  })
  description: string;

  @Column({
    type: DataType.TEXT,
    comment: '修复执行的SQL或操作步骤',
  })
  executedOperations?: string;

  @Column({
    type: DataType.INTEGER,
    comment: '修复耗时（毫秒）',
  })
  executionTime?: number;

  @Column({
    type: DataType.TEXT,
    comment: '错误信息（修复失败时记录）',
  })
  errorMessage?: string;

  @Column({
    type: DataType.TEXT,
    comment: '错误堆栈（修复失败时记录）',
  })
  errorStack?: string;

  @Column({
    type: DataType.INTEGER,
    comment: '操作人员ID',
  })
  @ForeignKey(() => Employee)
  operatorId?: number;

  @Column({
    type: DataType.STRING(100),
    comment: '操作人员姓名（冗余存储）',
  })
  operatorName?: string;

  @Column({
    type: DataType.ENUM('system', 'admin', 'employee'),
    allowNull: false,
    defaultValue: 'system',
    comment: '操作人员类型',
  })
  operatorType: 'system' | 'admin' | 'employee';

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否可回退',
  })
  canRevert: boolean;

  @Column({
    type: DataType.TEXT,
    comment: '回退数据（JSON格式，用于回退操作）',
  })
  revertData?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否已回退',
  })
  isReverted: boolean;

  @Column({
    type: DataType.DATE,
    comment: '回退时间',
  })
  revertedAt?: Date;

  @Column({
    type: DataType.INTEGER,
    comment: '回退操作人ID',
  })
  @ForeignKey(() => Employee)
  revertOperatorId?: number;

  @Column({
    type: DataType.STRING(100),
    comment: '回退操作人姓名',
  })
  revertOperatorName?: string;

  @Column({
    type: DataType.STRING(500),
    comment: '回退原因',
  })
  revertReason?: string;

  @Column({
    type: DataType.STRING(500),
    comment: '备注',
  })
  remark?: string;

  @BelongsTo(() => OrderAmountAnomalyRecord, { onDelete: 'CASCADE' })
  anomalyRecord?: OrderAmountAnomalyRecord;

  @BelongsTo(() => Order, { onDelete: 'CASCADE' })
  order?: Order;

  @BelongsTo(() => Employee, { 
    foreignKey: 'operatorId',
    onDelete: 'SET NULL' 
  })
  operator?: Employee;

  @BelongsTo(() => Employee, { 
    foreignKey: 'revertOperatorId',
    onDelete: 'SET NULL' 
  })
  revertOperator?: Employee;
}
