# 用户数据统计业务规则

## 1. 统计范围和基础规则

### 1.1 统计对象
- **统计范围**: 只统计状态为启用（status=1）的用户
- **排除条件**: 禁用状态（status=0）的用户不参与任何统计
- **数据来源**: customers表

### 1.2 时间范围
- **默认范围**: 不指定时间范围时，统计所有历史数据
- **时间过滤**: 基于用户创建时间（createdAt字段）进行过滤
- **时间格式**: YYYY-MM-DD格式，包含起始日期，包含结束日期

## 2. 用户概览统计规则

### 2.1 总用户数统计
- **计算方式**: 统计所有启用状态的用户总数
- **实时性**: 实时统计，反映当前最新数据

### 2.2 今日新增用户统计
- **时间范围**: 当天00:00:00到23:59:59
- **计算方式**: 统计当天创建且状态为启用的用户数量

### 2.3 本月新增用户统计
- **时间范围**: 当月1日00:00:00到当月最后一天23:59:59
- **计算方式**: 统计本月创建且状态为启用的用户数量

## 3. 性别分布统计规则

### 3.1 性别分类
- **0**: 女性用户
- **1**: 男性用户  
- **2**: 未知性别用户

### 3.2 统计计算
- **数量统计**: 分别统计各性别用户的数量
- **比例计算**: 各性别用户数量 / 总用户数 × 100%
- **精度要求**: 百分比保留两位小数
- **完整性**: 确保所有性别分类都有统计数据，即使数量为0

### 3.3 数据展示
- **排序规则**: 按性别编码升序排列（女-男-未知）
- **显示格式**: 同时显示性别编码、性别名称、数量和百分比

## 4. 会员状态分布统计规则

### 4.1 会员状态分类
- **0**: 普通会员
- **1**: 权益会员

### 4.2 统计计算
- **数量统计**: 分别统计各会员状态用户的数量
- **比例计算**: 各状态用户数量 / 总用户数 × 100%
- **精度要求**: 百分比保留两位小数
- **完整性**: 确保所有会员状态都有统计数据，即使数量为0

### 4.3 业务逻辑
- **默认状态**: 新注册用户默认为普通会员（memberStatus=0）
- **状态变更**: 购买权益卡后变为权益会员，权益卡过期或次数用完后变回普通会员
- **统计时点**: 统计当前时点的会员状态，不考虑历史状态变化

## 5. 注册趋势统计规则

### 5.1 统计周期
- **按日统计**: periodType=day，格式YYYY-MM-DD
- **按周统计**: periodType=week，格式YYYY-WW（年-周数）
- **按月统计**: periodType=month，格式YYYY-MM

### 5.2 时间范围要求
- **必填参数**: startDate和endDate为必填参数
- **范围限制**: 建议查询范围不超过1年，避免数据量过大
- **边界处理**: 包含起始日期和结束日期

### 5.3 数据聚合
- **分组规则**: 按指定的时间周期进行分组统计
- **排序规则**: 按时间周期升序排列
- **空值处理**: 没有注册用户的时间周期不返回数据

## 6. 数据一致性和性能规则

### 6.1 数据一致性
- **实时性**: 所有统计数据都是实时计算，确保数据准确性
- **事务性**: 统计查询不影响业务数据的事务处理
- **隔离性**: 统计查询使用只读方式，不锁定业务表

### 6.2 性能优化
- **索引要求**: 确保createdAt、status、gender、memberStatus字段有适当索引
- **查询优化**: 使用数据库聚合函数进行统计，避免应用层计算
- **缓存策略**: 对于频繁查询的统计数据，可考虑适当缓存

### 6.3 数据安全
- **权限控制**: 统计接口仅限管理端使用，需要相应权限
- **数据脱敏**: 统计结果不包含用户个人敏感信息
- **访问日志**: 记录统计接口的访问日志，便于审计

## 7. 异常处理规则

### 7.1 参数异常
- **日期格式错误**: 返回参数格式错误提示
- **日期范围异常**: 结束日期早于开始日期时返回错误
- **周期类型错误**: 不支持的periodType返回参数错误

### 7.2 数据异常
- **空数据处理**: 查询结果为空时返回空数组，不报错
- **计算异常**: 除零等计算异常时，百分比显示为0
- **数据库异常**: 数据库连接或查询异常时返回系统错误

## 8. 扩展性考虑

### 8.1 新增统计维度
- **地域分布**: 可基于用户地址信息扩展地域分布统计
- **活跃度统计**: 可基于最后登录时间扩展用户活跃度统计
- **来源渠道**: 可基于推广员工信息扩展用户来源统计

### 8.2 统计粒度优化
- **小时级统计**: 支持按小时统计注册趋势
- **自定义时间段**: 支持更灵活的时间范围选择
- **对比分析**: 支持同比、环比等对比分析功能

## 9. 监控和告警

### 9.1 数据监控
- **统计准确性**: 定期校验统计数据的准确性
- **性能监控**: 监控统计查询的响应时间
- **异常监控**: 监控统计接口的错误率和异常情况

### 9.2 业务告警
- **异常增长**: 用户注册数量异常增长或下降时告警
- **数据异常**: 统计数据出现明显异常时告警
- **性能告警**: 统计查询响应时间超过阈值时告警
