# 投诉建议API接口

## 功能概述

投诉建议系统支持三种来源的投诉建议：
- **客户投诉建议**: 由客户直接通过用户端提交
- **员工建议**: 由员工提交的改进建议
- **管理员录入**: 由管理员代为录入的投诉建议

系统能够自动识别来源类型，并在管理端提供分类查询和统计功能。

## 投诉建议状态枚举
```typescript
enum ComplaintStatus {
  pending = 'pending',      // 待处理
  processing = 'processing', // 处理中
  resolved = 'resolved',    // 已解决
  closed = 'closed'         // 已关闭
}

enum ComplaintCategory {
  complaint = 'complaint',   // 投诉
  suggestion = 'suggestion'  // 建议
}

enum ComplaintSubCategory {
  order = 'order',          // 订单
  employee = 'employee',    // 员工
  platform = 'platform',   // 平台
  service = 'service',      // 服务
  workflow = 'workflow'     // 流程
}

enum OperationType {
  create = 'create',           // 创建
  update = 'update',           // 更新
  status_change = 'status_change', // 状态变更
  handle = 'handle',           // 处理
  reply = 'reply',             // 回复
  delete = 'delete'            // 删除
}

enum OperatorType {
  customer = 'customer',       // 客户
  employee = 'employee',       // 员工
  admin = 'admin',             // 管理员
  system = 'system'            // 系统
}

enum SourceType {
  customer = 'customer',       // 客户投诉建议
  employee = 'employee',       // 员工建议
  admin = 'admin'              // 管理员录入
}
```

## 1. 查询接口

### 1.1 查询投诉建议列表（全部）
- **接口**: `GET /admin/complaints`
- **描述**: 管理端查询所有投诉建议列表，支持多条件筛选和分页，包含来源标识
- **参数**:
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `category` (string): 大类筛选 (complaint/suggestion)
  - `subCategory` (string): 小类筛选
  - `status` (string): 状态筛选
  - `customerId` (number): 客户ID筛选
  - `employeeId` (number): 员工ID筛选
  - `handlerId` (number): 处理人员ID筛选
  - `keyword` (string): 关键词搜索（标题和内容）
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期
  - `sourceType` (string): **新增** 来源类型筛选 (customer/employee/admin)

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "服务质量问题",
        "category": "complaint",
        "subCategory": "service",
        "status": "pending",
        "customerId": 123,
        "sourceType": "customer",
        "sourceName": "张三",
        "sourceInfo": {
          "type": "customer",
          "name": "张三",
          "description": "客户投诉建议"
        },
        "customer": {
          "id": 123,
          "nickname": "张三",
          "phone": "13800138000"
        },
        "createdAt": "2024-01-01T10:00:00.000Z"
      }
    ],
    "total": 100
  }
}
```

### 1.2 查询客户投诉建议
- **接口**: `GET /admin/complaints/customer-complaints`
- **描述**: 管理端专门查询客户投诉建议，只返回客户提交的投诉建议
- **参数**: 与1.1相同，但会自动过滤只显示客户投诉建议

### 1.3 查询员工建议
- **接口**: `GET /admin/complaints/employee-suggestions`
- **描述**: 管理端专门查询员工建议，只返回员工提交的建议
- **参数**: 与1.1相同，但会自动过滤只显示员工建议

### 1.4 查询投诉建议详情
- **接口**: `GET /admin/complaints/{id}`
- **描述**: 查询指定投诉建议的详细信息
- **参数**: `id` (number): 投诉建议ID

### 1.5 按客户查询投诉
- **接口**: `GET /admin/complaints/customer/{customerId}`
- **描述**: 查询指定客户的所有投诉建议
- **参数**: 
  - `customerId` (number): 客户ID
  - `page` (number): 页码
  - `pageSize` (number): 每页数量

### 1.6 按订单查询投诉
- **接口**: `GET /admin/complaints/order/{orderId}`
- **描述**: 查询指定订单的所有投诉建议
- **参数**:
  - `orderId` (number): 订单ID
  - `page` (number): 页码
  - `pageSize` (number): 每页数量

### 1.7 按员工查询投诉
- **接口**: `GET /admin/complaints/employee/{employeeId}`
- **描述**: 查询指定员工的所有投诉建议
- **参数**:
  - `employeeId` (number): 员工ID
  - `page` (number): 页码
  - `pageSize` (number): 每页数量

### 1.8 查询投诉统计摘要
- **接口**: `GET /admin/complaints/statistics/summary`
- **描述**: 获取投诉建议的统计摘要信息，包含来源统计
- **参数**:
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期
  - `category` (string): 大类筛选
  - `subCategory` (string): 小类筛选

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "total": 150,
    "statusStats": [
      { "status": "pending", "count": 50 },
      { "status": "processing", "count": 30 },
      { "status": "resolved", "count": 60 },
      { "status": "closed", "count": 10 }
    ],
    "categoryStats": [
      { "category": "complaint", "count": 100 },
      { "category": "suggestion", "count": 50 }
    ],
    "subCategoryStats": [
      { "subCategory": "order", "count": 60 },
      { "subCategory": "employee", "count": 20 },
      { "subCategory": "platform", "count": 40 },
      { "subCategory": "service", "count": 30 }
    ],
    "sourceStats": {
      "customer": {
        "count": 100,
        "label": "客户投诉建议",
        "description": "由客户直接提交的投诉建议"
      },
      "employee": {
        "count": 30,
        "label": "员工建议",
        "description": "由员工提交的改进建议"
      },
      "admin": {
        "count": 20,
        "label": "管理员录入",
        "description": "由管理员代为录入的投诉建议"
      },
      "total": 150
    }
  }
}
```

### 1.9 查询处理历史
- **接口**: `GET /admin/complaints/{id}/history`
- **描述**: 查询指定投诉建议的操作历史记录，支持分页
- **参数**:
  - `id` (number): 投诉建议ID
  - `page` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认20
- **返回数据**:
  ```json
  {
    "complaint": {
      "id": 1,
      "title": "投诉标题",
      "status": "resolved",
      // ... 其他投诉建议信息
    },
    "history": {
      "data": [
        {
          "id": 1,
          "operationType": "create",
          "operatorType": "customer",
          "operator": {
            "type": "customer",
            "id": 123,
            "name": "张三",
            "phone": "13800138000",
            "avatar": "https://example.com/avatar.jpg"
          },
          "beforeStatus": null,
          "afterStatus": "pending",
          "description": "用户创建投诉：服务质量问题",
          "operationDetails": {
            "category": "complaint",
            "subCategory": "service",
            "title": "服务质量问题",
            "content": "详细描述..."
          },
          "operatedAt": "2024-01-01T10:00:00.000Z"
        },
        {
          "id": 2,
          "operationType": "handle",
          "operatorType": "admin",
          "operator": {
            "type": "admin",
            "id": 1,
            "name": "管理员1"
          },
          "beforeStatus": "pending",
          "afterStatus": "resolved",
          "description": "管理员处理投诉建议，状态从\"pending\"变更为\"resolved\"",
          "operationDetails": {
            "result": "已联系客户并解决问题",
            "handlerId": 1,
            "handledAt": "2024-01-02T14:30:00.000Z"
          },
          "operatedAt": "2024-01-02T14:30:00.000Z"
        }
      ],
      "total": 2,
      "page": 1,
      "pageSize": 20,
      "totalPages": 1
    }
  }
  ```

### 1.10 查询管理员录入的投诉
- **接口**: `GET /admin/complaints/admin-created`
- **描述**: 查询管理员录入的投诉建议列表
- **参数**: 
  - `page` (number): 页码
  - `pageSize` (number): 每页数量
  - `createdBy` (number): 录入人员ID

## 2. 操作接口

### 2.1 管理员录入投诉建议
- **接口**: `POST /admin/complaints`
- **描述**: 管理端录入投诉建议，可以代表客户录入或录入不关联客户的投诉
- **说明**: 录入人员ID会从登录信息中自动获取，无需前端传递
- **请求体**:
  ```json
  {
    "category": "complaint",                    // 必填：complaint/suggestion
    "subCategory": "order",                     // 必填：order/employee/platform/service/workflow
    "title": "订单服务问题",                    // 必填：标题，最大200字符
    "content": "服务质量不满意，希望改进",       // 必填：内容，最大2000字符
    "customerId": 123,                          // 可选：客户ID
    "orderId": 456,                             // 可选：订单ID（订单投诉时必填）
    "employeeId": 789,                          // 可选：员工ID（人员投诉时必填）
    "contactInfo": "13800138000",               // 可选：联系方式，最大100字符
    "photoURLs": ["https://example.com/photo1.jpg"],  // 可选：图片URL数组，最多6张
    "adminNote": "客户电话反馈"                 // 可选：管理员录入备注，最大500字符
  }
  ```

### 2.2 用户提交投诉建议
- **接口**: `POST /user/complaints`
- **描述**: 用户端提交投诉建议
- **请求体**: 
  ```json
  {
    "category": "complaint",
    "subCategory": "order",
    "title": "订单服务问题",
    "content": "服务质量不满意，希望改进",
    "orderId": 456,                             // 订单投诉时必填
    "employeeId": 789,                          // 人员投诉时必填
    "contactInfo": "13800138000",
    "photoURLs": ["https://example.com/photo1.jpg"]
  }
  ```

### 2.3 更新投诉建议
- **接口**: `PUT /admin/complaints/{id}`
- **描述**: 管理员更新投诉建议信息
- **参数**: `id` (number): 投诉建议ID
- **请求体**: 包含需要更新的字段

### 2.4 处理投诉建议
- **接口**: `POST /admin/complaints/{id}/handle`
- **描述**: 管理员处理投诉建议
- **说明**: 处理人员ID会从登录信息中自动获取，无需前端传递
- **参数**: `id` (number): 投诉建议ID
- **请求体**:
  ```json
  {
    "status": "resolved",                       // 新状态
    "result": "已联系客户并解决问题"            // 处理结果
  }
  ```

### 2.5 快速更新投诉状态
- **接口**: `PATCH /admin/complaints/{id}/status`
- **描述**: 快速更新投诉建议状态
- **参数**: `id` (number): 投诉建议ID
- **请求体**: 
  ```json
  {
    "status": "processing"                      // 新状态
  }
  ```

### 2.6 删除投诉建议
- **接口**: `DELETE /admin/complaints/{id}`
- **描述**: 删除投诉建议（管理员权限）
- **参数**: `id` (number): 投诉建议ID

## 3. 批量操作接口

### 3.1 批量处理投诉建议
- **接口**: `POST /admin/complaints/batch-handle`
- **描述**: 批量处理多个投诉建议
- **说明**: 处理人员ID会从登录信息中自动获取，无需前端传递
- **请求体**:
  ```json
  {
    "ids": [1, 2, 3],                          // 投诉建议ID数组
    "handleData": {
      "status": "resolved",                     // 新状态
      "result": "批量处理完成"                  // 处理结果
    }
  }
  ```

### 3.2 批量删除投诉建议
- **接口**: `DELETE /admin/complaints/batch`
- **描述**: 批量删除多个投诉建议
- **请求体**: 
  ```json
  {
    "ids": [1, 2, 3]                           // 投诉建议ID数组
  }
  ```

## 4. 用户端接口

### 4.1 查询用户投诉列表
- **接口**: `GET /user/complaints`
- **描述**: 用户查询自己的投诉建议列表
- **参数**: 
  - `page` (number): 页码
  - `pageSize` (number): 每页数量
  - `status` (string): 状态筛选

### 4.2 查询用户投诉详情
- **接口**: `GET /user/complaints/{id}`
- **描述**: 用户查询自己的投诉建议详情
- **参数**: `id` (number): 投诉建议ID

### 4.3 用户撤销投诉
- **接口**: `DELETE /user/complaints/{id}`
- **描述**: 用户撤销自己的投诉建议（仅限待处理状态）
- **参数**: `id` (number): 投诉建议ID

## 5. 员工端接口

### 5.1 查询相关投诉
- **接口**: `GET /employee/complaints`
- **描述**: 员工查询与自己相关的投诉建议
- **参数**: 
  - `page` (number): 页码
  - `pageSize` (number): 每页数量
  - `status` (string): 状态筛选

### 5.2 员工回复投诉
- **接口**: `POST /employee/complaints/{id}/reply`
- **描述**: 员工对投诉建议进行回复
- **参数**: `id` (number): 投诉建议ID
- **请求体**: 
  ```json
  {
    "reply": "感谢您的反馈，我们会改进服务质量"
  }
  ```

## 业务规则说明

### 录入规则
- 订单投诉（subCategory=order）时，orderId必填
- 人员投诉（subCategory=employee）时，employeeId必填
- 订单投诉时，系统会自动从订单获取服务人员信息并关联
- customerId可为空，支持录入不关联客户的投诉

### 权限规则
- 管理员拥有最高权限，可以操作任何状态的投诉建议
- 用户只能查看和操作自己的投诉建议
- 员工只能查看与自己相关的投诉建议

### 状态流转
- pending → processing → resolved/closed
- 用户只能撤销待处理状态的投诉
- 管理员可以在任何状态间切换

### 操作记录规则
- 系统自动记录所有对投诉建议的操作（创建、更新、状态变更、处理、回复、删除）
- 操作记录包含操作人信息、操作时间、操作前后状态、操作描述和详细信息
- 操作记录支持分页查询，按操作时间倒序排列
- 删除投诉建议时，相关的操作记录会被级联删除
- 操作记录中的操作人信息会冗余存储，避免关联数据删除后无法识别操作人

## 来源区分功能

### 来源类型说明

| 来源类型 | 判断条件 | 显示名称 | 描述 | 接口路径 |
|----------|----------|----------|------|----------|
| **customer** | `customerId` 不为空 | 客户昵称 | 客户投诉建议 | 用户端提交 |
| **employee** | `customerId` 为空且 `employeeId` 不为空 | 员工姓名 | 员工建议 | 员工端提交 |
| **admin** | `createdBy` 不为空 | 管理员录入 | 管理员录入 | 管理端录入 |

### 管理端查询方式

1. **查询所有投诉建议**: `GET /admin/complaints` - 包含所有来源，带来源标识
2. **只查询客户投诉**: `GET /admin/complaints/customer-complaints` - 只显示客户投诉
3. **只查询员工建议**: `GET /admin/complaints/employee-suggestions` - 只显示员工建议
4. **只查询管理员录入**: `GET /admin/complaints/admin-created` - 只显示管理员录入
5. **按来源类型筛选**: `GET /admin/complaints?sourceType=customer` - 使用参数筛选

### 响应数据结构

所有管理端查询接口都会返回以下来源标识字段：

```json
{
  "sourceType": "customer",           // 来源类型
  "sourceName": "张三",               // 来源名称
  "sourceInfo": {                     // 详细来源信息
    "type": "customer",
    "name": "张三",
    "description": "客户投诉建议"
  }
}
```

### 统计功能

管理端统计接口 `GET /admin/complaints/statistics/summary` 会返回：
- 基础统计（状态、类型、子类型）
- **来源统计**（各来源类型的数量分布）

### 前端展示建议

- 使用不同颜色或图标区分来源类型
- 支持按来源类型筛选和排序
- 在列表中显示来源标识
- 在详情页显示详细来源信息

### 注意事项

1. **用户端和员工端接口保持不变**，只增强管理端查询功能
2. **历史数据兼容**：系统会根据现有字段自动判断来源类型
3. **权限控制**：不同来源的投诉建议遵循相同的权限规则
4. **数据一致性**：来源标识基于数据库字段实时计算，确保准确性
