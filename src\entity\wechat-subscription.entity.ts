import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Order } from './order.entity';

export interface WechatSubscriptionAttributes {
  /** 主键ID */
  id: number;
  /** 用户OpenID */
  openId: string;
  /** 模板ID */
  templateId: string;
  /** 关联订单ID */
  orderId: string;
  /** 订阅状态 */
  status: boolean;
  /** 订阅时间 */
  subscribeTime?: Date;
  /** 取消订阅时间 */
  unsubscribeTime?: Date;
  /** 最后发送时间 */
  lastSendTime?: Date;
  /** 关联的订单 */
  order?: Order;
}

@Table({
  tableName: 'wechat_subscription',
  timestamps: false,
  comment: '微信消息订阅表',
})
export class WechatSubscription
  extends Model<WechatSubscriptionAttributes>
  implements WechatSubscriptionAttributes
{
  @Column({
    type: DataType.BIGINT.UNSIGNED,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    unique: {
      name: 'uk_openid_template_orderid',
      msg: '同一个订单不能重复订阅',
    },
    field: 'open_id',
    comment: '用户OpenID',
  })
  openId: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    unique: {
      name: 'uk_openid_template_orderid',
      msg: '同一个订单不能重复订阅',
    },
    field: 'template_id',
    comment: '模板ID',
  })
  templateId: string;

  @ForeignKey(() => Order)
  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: {
      name: 'uk_openid_template_orderid',
      msg: '同一个订单不能重复订阅',
    },
    field: 'order_id',
    comment: '关联订单ID',
  })
  orderId: string;

  @BelongsTo(() => Order, {
    foreignKey: 'order_id',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  order?: Order;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '订阅状态',
  })
  status: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    field: 'subscribe_time',
    comment: '订阅时间',
  })
  subscribeTime: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    field: 'unsubscribe_time',
    comment: '取消订阅时间',
  })
  unsubscribeTime?: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    field: 'last_send_time',
    comment: '最后发送时间',
  })
  lastSendTime?: Date;
}
