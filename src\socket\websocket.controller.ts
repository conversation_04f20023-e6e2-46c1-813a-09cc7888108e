import {
  WSController,
  OnWSConnection,
  OnWSMessage,
  OnWSDisConnection,
  Inject,
  Logger,
  WSBroadCast,
} from '@midwayjs/core';
import { Context } from '@midwayjs/ws';
import { ILogger } from '@midwayjs/logger';
import * as http from 'http';

/**
 * 简单高性能的 WebSocket 控制器
 * 仅支持文本消息
 */
@WSController()
export class WebSocketController {
  @Inject()
  ctx: Context;

  @Logger()
  logger: ILogger;

  /**
   * 客户端连接时触发
   */
  @OnWSConnection()
  async onConnection(socket: Context, request: http.IncomingMessage) {
    const clientId = `client_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 6)}`;

    // 简单存储客户端信息
    (this.ctx as any).clientId = clientId;
    (this.ctx as any).connectedAt = new Date();

    this.logger.info(`WebSocket 客户端连接: ${clientId}`);

    // 发送连接成功消息
    // this.ctx.send(
    //   JSON.stringify({
    //     type: 'connected',
    //     clientId,
    //     message: '连接成功',
    //     timestamp: Date.now(),
    //   })
    // );
  }

  /**
   * 接收并广播文本消息
   */
  @OnWSMessage('message')
  @WSBroadCast()
  async onMessage(data: string) {
    const clientId = (this.ctx as any).clientId;

    // 简单的消息处理
    let message: any;
    try {
      message = JSON.parse(data);
    } catch {
      // 如果不是JSON，当作纯文本处理
      message = { type: 'text', content: data };
    }

    // 处理心跳
    if (message.type === 'ping') {
      return { type: 'pong', timestamp: Date.now() };
    }

    // 广播消息
    return {
      type: 'message',
      clientId,
      content: message.content || data,
      timestamp: Date.now(),
    };
  }

  /**
   * 客户端断开连接
   */
  @OnWSDisConnection()
  async onDisconnection() {
    const clientId = (this.ctx as any).clientId;
    if (clientId) {
      this.logger.info(`WebSocket 客户端断开连接: ${clientId}`);
    }
  }
}
