# 投诉建议业务规则

## 投诉建议分类

### 大类分类
- **投诉(complaint)**: 对服务不满意的反馈
- **建议(suggestion)**: 对服务改进的建议

### 小类分类
- **订单(order)**: 与订单相关的投诉建议
- **员工(employee)**: 与员工服务相关的投诉建议
- **平台(platform)**: 与平台功能相关的投诉建议
- **服务(service)**: 与服务质量相关的投诉建议
- **流程(workflow)**: 与业务流程相关的投诉建议

## 状态流转规则

### 状态定义
- **待处理(pending)**: 刚提交的投诉建议
- **处理中(processing)**: 正在处理的投诉建议
- **已解决(resolved)**: 已经解决的投诉建议
- **已关闭(closed)**: 已关闭的投诉建议

### 流转规则
```
待处理 → 处理中 → 已解决/已关闭
```

### 状态权限
- **用户**: 只能撤销待处理状态的投诉
- **员工**: 可以回复相关投诉
- **管理员**: 可以在任何状态间切换

## 录入规则

### 用户录入
- **身份验证**: 必须是已登录的用户
- **关联信息**: 自动关联用户信息
- **必填字段**: 大类、小类、标题、内容

### 管理员录入
- **代表录入**: 可以代表客户录入投诉
- **独立录入**: 可以录入不关联客户的投诉
- **录入标识**: 自动标记为管理员录入
- **录入人员**: 从登录信息自动获取，不允许前端传递

### 字段验证
- **订单投诉**: subCategory为order时，orderId必填
- **人员投诉**: subCategory为employee时，employeeId必填
- **图片限制**: 最多上传6张图片
- **内容长度**: 标题最大200字符，内容最大2000字符

## 自动关联规则

### 订单投诉自动关联
- **服务人员**: 订单投诉时自动从订单信息中获取服务人员ID
- **客户信息**: 自动关联订单的客户信息
- **服务信息**: 自动关联订单的服务信息

### 员工投诉处理
- **直接指定**: 人员投诉时直接指定员工ID
- **通知机制**: 自动通知相关员工

## 处理规则

### 处理权限
- **管理员**: 拥有最高权限，可以处理任何状态的投诉
- **处理人员**: 从登录信息自动获取，不允许前端传递
- **处理记录**: 记录处理人员和处理时间

### 处理流程
1. **接收投诉**: 系统接收投诉建议
2. **分配处理**: 分配给相关处理人员
3. **调查处理**: 调查问题并制定解决方案
4. **反馈结果**: 向投诉人反馈处理结果
5. **跟踪回访**: 跟踪处理效果

### 批量处理
- **批量操作**: 支持批量处理多个投诉建议
- **统一处理**: 使用统一的处理结果和状态
- **处理人员**: 批量处理时统一使用当前登录管理员

## 权限控制

### 查看权限
- **用户**: 只能查看自己的投诉建议
- **员工**: 只能查看与自己相关的投诉建议
- **管理员**: 可以查看所有投诉建议

### 操作权限
- **用户**: 可以提交和撤销自己的投诉
- **员工**: 可以回复相关投诉
- **管理员**: 可以进行所有操作

### 数据权限
- **敏感信息**: 保护客户隐私信息
- **内部信息**: 管理员备注等内部信息不对外显示

## 通知机制

### 状态变更通知
- **用户通知**: 投诉状态变更时通知用户
- **员工通知**: 相关投诉时通知员工
- **管理员通知**: 新投诉时通知管理员

### 通知方式
- **系统消息**: 系统内消息通知
- **微信推送**: 微信小程序消息推送
- **短信通知**: 重要投诉的短信通知

## 数据统计

### 统计维度
- **投诉类型**: 按大类和小类统计
- **处理状态**: 按状态统计投诉数量
- **处理时效**: 统计平均处理时间
- **满意度**: 统计客户满意度

### 统计报表
- **日报**: 每日投诉建议统计
- **周报**: 每周投诉建议汇总
- **月报**: 每月投诉建议分析

## 质量管理

### 处理质量
- **处理时效**: 设定处理时效标准
- **处理质量**: 评估处理结果质量
- **客户满意度**: 收集客户满意度反馈

### 改进机制
- **问题分析**: 分析投诉建议中的问题
- **改进措施**: 制定针对性改进措施
- **效果跟踪**: 跟踪改进措施的效果

## 数据一致性

### 关联数据同步
- **客户信息**: 保持与客户信息的同步
- **订单信息**: 保持与订单信息的同步
- **员工信息**: 保持与员工信息的同步

### 数据完整性
- **必要信息**: 确保投诉建议包含必要信息
- **历史记录**: 保留完整的处理历史记录
- **数据备份**: 定期备份重要数据

## 特殊场景处理

### 恶意投诉
- **识别机制**: 识别恶意投诉的特征
- **处理策略**: 制定恶意投诉的处理策略
- **防护措施**: 建立防护机制

### 重复投诉
- **去重检查**: 检查是否为重复投诉
- **合并处理**: 合并相同问题的投诉
- **统一回复**: 对重复投诉统一回复

### 紧急投诉
- **优先级**: 设置投诉优先级
- **快速通道**: 紧急投诉的快速处理通道
- **升级机制**: 重要投诉的升级机制

## 性能优化

### 查询优化
- **索引设计**: 在常用查询字段上建立索引
- **分页查询**: 大数据量查询使用分页
- **缓存策略**: 缓存常用的统计数据

### 存储优化
- **图片存储**: 优化图片存储和访问
- **数据归档**: 定期归档历史数据
- **压缩存储**: 对大文本内容进行压缩

## 监控告警

### 业务监控
- **投诉量**: 监控投诉建议的数量变化
- **处理时效**: 监控处理时效是否达标
- **满意度**: 监控客户满意度变化

### 系统监控
- **接口性能**: 监控相关接口的性能
- **数据一致性**: 监控数据一致性
- **异常处理**: 监控异常情况的处理

### 告警机制
- **阈值告警**: 设置关键指标的告警阈值
- **实时告警**: 重要问题的实时告警
- **定期报告**: 定期生成监控报告
