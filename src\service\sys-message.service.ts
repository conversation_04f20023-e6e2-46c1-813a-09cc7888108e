import { ILogger, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { SysMessage, MessageTemplate } from '../entity';
import { CustomError } from '../error/custom.error';

@Provide()
export class MessageService {
  @Inject()
  ctx: Context;

  @Inject()
  logger: ILogger;

  /**
   * 获取用户消息列表
   * @param userId 用户ID
   * @param type 消息类型
   * @param page 页码
   * @param limit 每页数量
   */
  async getUserMessages(
    userId: number,
    type?: 'system' | 'platform' | 'order',
    page = 1,
    limit = 20
  ) {
    const offset = (page - 1) * limit;

    // 构建查询条件
    const whereCondition: any = { userId };
    if (type) {
      whereCondition.type = type;
    }

    // 查询消息列表
    const { rows: list, count: total } = await SysMessage.findAndCountAll({
      where: whereCondition,
      order: [['createdAt', 'DESC']],
      offset,
      limit,
    });

    return {
      list: list.map(item => ({
        id: item.id,
        type: item.type,
        title: item.title,
        content: item.content,
        extraData: item.extraData ? JSON.parse(item.extraData) : null,
        isRead: item.isRead,
        createdAt: item.createdAt,
        readAt: item.readAt,
      })),
      total,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 获取消息详情
   * @param messageId 消息ID
   */
  async getMessageDetail(messageId: number) {
    const message = await SysMessage.findByPk(messageId);

    if (!message) {
      throw new CustomError('消息不存在');
    }

    return {
      id: message.id,
      type: message.type,
      title: message.title,
      content: message.content,
      extraData: message.extraData ? JSON.parse(message.extraData) : null,
      isRead: message.isRead,
      createdAt: message.createdAt,
      readAt: message.readAt,
    };
  }

  /**
   * 标记消息为已读
   * @param messageId 消息ID
   */
  async markAsRead(messageId: number) {
    const message = await SysMessage.findByPk(messageId);

    if (!message) {
      throw new CustomError('消息不存在');
    }

    if (message.isRead) {
      return {
        messageId,
        readAt: message.readAt,
      };
    }

    const readAt = new Date();
    await message.update({
      isRead: true,
      readAt,
    });

    return {
      messageId,
      readAt,
    };
  }

  /**
   * 标记所有消息为已读
   * @param userId 用户ID
   */
  async markAllAsRead(userId: number) {
    const markedAt = new Date();

    // 更新所有未读消息
    const [affectedCount] = await SysMessage.update(
      {
        isRead: true,
        readAt: markedAt,
      },
      {
        where: {
          userId,
          isRead: false,
        },
      }
    );

    return {
      userId,
      markedCount: affectedCount,
      markedAt,
    };
  }

  /**
   * 删除消息（软删除）
   * @param messageId 消息ID
   */
  async deleteMessage(messageId: number) {
    const message = await SysMessage.findByPk(messageId);

    if (!message) {
      throw new CustomError('消息不存在');
    }

    await message.destroy();

    return {
      messageId,
      deletedAt: new Date(),
    };
  }

  /**
   * 获取未读消息数量
   * @param userId 用户ID
   */
  async getUnreadCount(userId: number) {
    // 获取总未读数量
    const unreadCount = await SysMessage.count({
      where: {
        userId,
        isRead: false,
      },
    });

    // 按类型统计未读数量
    const countByType = await SysMessage.findAll({
      attributes: [
        'type',
        [
          SysMessage.sequelize.fn('COUNT', SysMessage.sequelize.col('id')),
          'count',
        ],
      ],
      where: {
        userId,
        isRead: false,
      },
      group: ['type'],
      raw: true,
    });

    // 格式化按类型统计结果
    const typeCountMap = {
      system: 0,
      platform: 0,
      order: 0,
    };

    countByType.forEach((item: any) => {
      typeCountMap[item.type] = parseInt(item.count);
    });

    return {
      userId,
      unreadCount,
      countByType: typeCountMap,
    };
  }

  /**
   * 创建消息
   * @param messageData 消息数据
   */
  async createMessage(messageData: {
    userId: number;
    type: 'system' | 'platform' | 'order';
    title: string;
    content: string;
    extraData?: any;
  }) {
    const { userId, type, title, content, extraData } = messageData;

    const message = await SysMessage.create({
      userId,
      type,
      title,
      content,
      extraData: extraData ? JSON.stringify(extraData) : null,
      isRead: false,
    });

    return {
      id: message.id,
      userId: message.userId,
      type: message.type,
      title: message.title,
      content: message.content,
      extraData: message.extraData ? JSON.parse(message.extraData) : null,
      isRead: message.isRead,
      createdAt: message.createdAt,
    };
  }

  /**
   * 根据模板创建消息
   * @param templateCode 模板代码
   * @param userId 用户ID
   * @param variables 模板变量
   */
  async createMessageFromTemplate(
    templateCode: string,
    userId: number,
    variables: Record<string, any> = {}
  ) {
    // 查找模板
    const template = await MessageTemplate.findOne({
      where: {
        code: templateCode,
        isActive: true,
      },
    });

    if (!template) {
      throw new CustomError(`消息模板不存在: ${templateCode}`);
    }

    // 替换模板变量
    let title = template.title;
    let content = template.content;

    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      title = title.replace(new RegExp(placeholder, 'g'), variables[key]);
      content = content.replace(new RegExp(placeholder, 'g'), variables[key]);
    });

    // 创建消息
    return await this.createMessage({
      userId,
      type: template.type,
      title,
      content,
      extraData: variables,
    });
  }
}
