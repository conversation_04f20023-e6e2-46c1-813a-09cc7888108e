import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Order } from './order.entity';
import { Customer } from './customer.entity';

export interface ReviewAttributes {
  /** 评价ID */
  id: number;
  /** 关联订单ID */
  orderId: number;
  /** 关联客户ID */
  customerId: number;
  /** 评分 */
  rating: number;
  /** 文字评价 */
  comment?: string;
  /** 照片链接列表 */
  photoURLs?: string[];
  /** 关联的订单信息 */
  order?: Order;
  /** 关联的客户信息 */
  customer?: Customer;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({ tableName: 'reviews', timestamps: true, comment: '评价表' })
export class Review
  extends Model<ReviewAttributes>
  implements ReviewAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '评价ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联订单ID',
  })
  @ForeignKey(() => Order)
  orderId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联客户ID',
  })
  @ForeignKey(() => Customer)
  customerId: number;

  @Column({
    type: DataType.TINYINT,
    allowNull: false,
    comment: '评分',
  })
  rating: number;

  @Column({
    type: DataType.TEXT,
    comment: '文字评价',
  })
  comment: string;

  @Column({
    type: DataType.JSON,
    comment: '照片链接列表',
  })
  photoURLs: string[];

  @BelongsTo(() => Order)
  order: Order;

  @BelongsTo(() => Customer)
  customer: Customer;
}
