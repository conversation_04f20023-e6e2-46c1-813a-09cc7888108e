# 订单金额异常检查接口

## 概述

订单金额异常检查系统提供简化的API接口，专门用于检测和修复订单金额异常，服务于收入统计数据验证。系统专注于检测支付计算逻辑异常，确保 `原价 - 权益卡优惠 - 代金券优惠 = 实付金额` 的计算正确性。

## 核心接口

### 1. 检查异常并生成修复建议

**接口地址：** `GET /admin/order-amount-anomalies/check`

**接口描述：** 检查订单支付计算异常并生成详细的修复建议，专注于检测支付逻辑错误

**请求参数：**
- `clearExistingRecords` (string, 可选): 是否在检测前清除所有异常记录，默认false
- `orderId` (number, 可选): 指定订单ID，只检查该订单

**响应示例：**
```json
{
  "anomalyCount": 1,
  "suggestions": [
    {
      "orderId": 136,
      "orderSn": "ORD20231201001",
      "anomalyType": "discount_anomaly",
      "description": "优惠金额(110.6)超过订单原价(98)",
      "severity": 4,
      "currentData": {
        "originalPrice": 98.00,
        "totalFee": 2.40,
        "cardDeduction": 22.60,
        "couponDeduction": 88.00
      },
      "calculatedOriginalPrice": 98.00,
      "expectedTotalFee": 2.40,
      "suggestions": [
        {
          "suggestionId": "proportional_adjust",
          "type": "fix_discount",
          "title": "按比例调整优惠金额（保持实付不变）",
          "description": "保持实付金额2.4元不变，将优惠从110.6元按比例调整为95.6元",
          "action": {
            "field": "discount",
            "cardDeduction": 19.66,
            "couponDeduction": 75.94,
            "reason": "按比例缩减优惠金额，保持实付金额不变"
          },
          "risk": "medium",
          "recommended": true,
          "warning": "⚠️ 此方案保持实付金额不变，按比例缩减两种优惠"
        },
        {
          "suggestionId": "keep_card_priority",
          "type": "fix_discount",
          "title": "优先保留权益卡优惠",
          "description": "保持权益卡22.6元，将代金券调整为73.0元",
          "action": {
            "field": "discount",
            "cardDeduction": 22.60,
            "couponDeduction": 73.00,
            "reason": "优先保留权益卡优惠，调整代金券金额"
          },
          "risk": "medium",
          "recommended": false,
          "warning": "⚠️ 此方案优先保留权益卡优惠，可能大幅减少代金券金额"
        }
      ],
      "manualReviewRequired": false
    }
  ]
}
```

### 2. 应用修复方案

**接口地址：** `POST /admin/order-amount-anomalies/apply-fix`

**接口描述：** 应用用户选定的修复方案

**请求体：**
```json
{
  "orderId": 123,
  "fixType": "fix_original_price",
  "value": 100.00,
  "operatorId": 1,
  "operatorName": "管理员",
  "remark": "根据订单明细修正原价",
  "confirmRisk": true
}
```

**参数说明：**
- `orderId` (number, 必填): 订单ID
- `suggestionId` (string, 必填): 修复方案ID，从检查接口返回的建议中获取，可选值：
  - `set_original_price`: 设置原价（适用于原价缺失）
  - `fix_total_fee`: 修正实付金额（高风险，需确认银行流水）
  - `reverse_original_price`: 根据实付金额反推原价
  - `adjust_discount`: 调整优惠金额
  - `proportional_adjust`: 按比例调整优惠金额（保持实付不变）
  - `keep_card_priority`: 优先保留权益卡优惠
  - `keep_coupon_priority`: 优先保留代金券优惠
  - `fix_original_price`: 修正原价以覆盖优惠金额
- `operatorId` (number, 必填): 操作人ID
- `operatorName` (string, 必填): 操作人姓名
- `remark` (string, 可选): 备注说明
- `confirmRisk` (boolean, 可选): 高风险操作确认标志

## 使用流程

### 简化的异常检查和修复流程

```bash
# 1. 检查异常并生成修复建议
GET /admin/order-amount-anomalies/check?clearExistingRecords=true

# 2. 应用修复方案（按比例调整优惠 - 推荐）
POST /admin/order-amount-anomalies/apply-fix
{
  "orderId": 136,
  "suggestionId": "proportional_adjust",
  "operatorId": 1,
  "operatorName": "管理员",
  "remark": "按比例调整优惠金额，保持实付金额不变"
}

# 3. 应用修复方案（优先保留权益卡）
POST /admin/order-amount-anomalies/apply-fix
{
  "orderId": 136,
  "suggestionId": "keep_card_priority",
  "operatorId": 1,
  "operatorName": "管理员",
  "remark": "优先保留权益卡优惠，调整代金券金额"
}

# 4. 验证修复效果
GET /admin/revenue-statistics/overview
```

### 修复方案选择原则

- 🟡 **修正原价**: 风险中等，适用于原价缺失或明确需要修正的情况
- 🟡 **调整优惠**: 风险中等，需要确认业务逻辑
- 🔴 **修正实付**: 风险高，需要确认银行流水，谨慎操作

**建议优先级**: 修正原价 > 调整优惠 > 修正实付金额

## 检测逻辑说明

### 核心检测原则

系统专注于检测**支付计算逻辑异常**，确保以下公式成立：
```
实付金额 = 原价 - 权益卡抵扣 - 代金券抵扣
```

### 检测范围

✅ **检测的异常类型**:
- **原价缺失**: 原价为空或0的情况
- **支付计算错误**: 实付金额与计算结果不符
- **优惠金额异常**: 优惠总额超过原价（系统会提供调整优惠或修正原价的建议）

### 优惠金额异常处理策略

当检测到优惠金额超过原价时，系统会提供多种修复方案：

1. **按比例调整优惠金额（推荐）**：保持实付金额不变，按比例缩减权益卡和代金券优惠
2. **优先保留权益卡优惠**：保持权益卡金额不变，调整代金券金额
3. **优先保留代金券优惠**：保持代金券金额不变，调整权益卡金额
4. **修正原价**：将原价调整为能够覆盖当前优惠金额的值（高风险）

### 设计理念

系统专注于检测支付计算逻辑的正确性，确保订单金额数据的一致性和准确性。对于优惠金额异常，优先考虑保持实付金额不变的修复方案。

## 注意事项

1. **数据备份**: 修复前建议备份订单数据
2. **风险控制**: 高风险操作需要额外确认
3. **操作记录**: 所有修复操作都有详细日志
4. **检测逻辑**: 系统专注于检测支付计算逻辑：原价 - 优惠 = 实付金额
