import {
  Controller,
  Post,
  Get,
  Del,
  Body,
  Param,
  Query,
  Inject,
} from '@midwayjs/core';
import { EmployeeCheckInService } from '../service/employee-checkin.service';
import { CustomError } from '../error/custom.error';

@Controller('/employee-checkins')
export class EmployeeCheckInController {
  @Inject()
  employeeCheckInService: EmployeeCheckInService;

  @Get('/', { summary: '管理端查询所有员工打卡记录' })
  async findAll(
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10,
    @Query('employeeId') employeeId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('keyword') keyword?: string
  ) {
    return await this.employeeCheckInService.findAllCheckIns(
      current,
      pageSize,
      employeeId,
      startDate,
      endDate,
      keyword
    );
  }

  @Get('/statistics', { summary: '获取打卡统计信息' })
  async getStatistics(
    @Query('employeeId') employeeId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    return await this.employeeCheckInService.getStatistics(
      employeeId,
      startDate,
      endDate
    );
  }

  @Get('/:id', { summary: '查询打卡记录详情' })
  async findById(@Param('id') id: number) {
    const checkIn = await this.employeeCheckInService.findOne({
      where: { id },
      include: ['employee'],
    });
    if (!checkIn) {
      throw new CustomError('打卡记录不存在', 404);
    }
    return checkIn;
  }

  @Del('/:id', { summary: '管理端删除打卡记录' })
  async delete(@Param('id') id: number) {
    return await this.employeeCheckInService.deleteCheckIn(id);
  }

  @Get('/employee/:employeeId', { summary: '管理端查询指定员工打卡记录' })
  async findByEmployee(
    @Param('employeeId') employeeId: number,
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    return await this.employeeCheckInService.findByEmployeeId(
      employeeId,
      current,
      pageSize,
      startDate,
      endDate
    );
  }

  @Get('/employee/:employeeId/statistics', {
    summary: '管理端查询指定员工打卡统计',
  })
  async getEmployeeStatistics(
    @Param('employeeId') employeeId: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    return await this.employeeCheckInService.getStatistics(
      employeeId,
      startDate,
      endDate
    );
  }

  @Get('/employee/:employeeId/last-checkin', {
    summary: '查询员工最后一次打卡时间',
  })
  async getLastCheckInTime(@Param('employeeId') employeeId: number) {
    return await this.employeeCheckInService.getLastCheckInTime(employeeId);
  }

  @Post('/batch-delete', { summary: '批量删除打卡记录' })
  async batchDelete(@Body() body: { ids: number[] }) {
    if (!body.ids || body.ids.length === 0) {
      throw new CustomError('请选择要删除的记录', 400);
    }

    const results = [];
    for (const id of body.ids) {
      try {
        await this.employeeCheckInService.deleteCheckIn(id);
        results.push({ id, success: true });
      } catch (error) {
        results.push({ id, success: false, error: error.message });
      }
    }

    return {
      results,
      successCount: results.filter(r => r.success).length,
      failCount: results.filter(r => !r.success).length,
    };
  }
}
