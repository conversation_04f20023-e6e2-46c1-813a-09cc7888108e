import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { EmployeePromotionService } from '../service/employee-promotion.service';
import { CustomError } from '../error/custom.error';

@Controller('/employee-promotion')
export class EmployeePromotionController {
  @Inject()
  ctx: Context;

  @Inject()
  service: EmployeePromotionService;

  @Post('/create', { summary: '用户填写推广码建立推广关系' })
  async createPromotionRelation(@Body() body: any) {
    const { customerId, promotionCode } = body;

    if (!customerId || !promotionCode) {
      throw new CustomError('客户ID和推广码不能为空');
    }

    return await this.service.createPromotionRelation(
      customerId,
      promotionCode
    );
  }

  @Get('/check-code/:promotionCode', { summary: '检查推广码是否可用' })
  async checkPromotionCode(@Param('promotionCode') promotionCode: string) {
    if (!promotionCode) {
      throw new CustomError('推广码不能为空');
    }

    return await this.service.checkPromotionCodeAvailable(promotionCode);
  }

  @Get('/employee/:employeeId/customers', { summary: '获取员工推广的客户列表' })
  async getEmployeePromotedCustomers(
    @Param('employeeId') employeeId: string,
    @Query() query: any
  ) {
    const { current = 1, pageSize = 10 } = query;

    return await this.service.getEmployeePromotedCustomers(
      parseInt(employeeId),
      parseInt(current),
      parseInt(pageSize)
    );
  }

  @Get('/customer/:customerId/employee', { summary: '获取客户的推广员工信息' })
  async getCustomerPromotionEmployee(@Param('customerId') customerId: string) {
    return await this.service.getCustomerPromotionEmployee(
      parseInt(customerId)
    );
  }

  @Get('/employee/:employeeId/statistics', { summary: '获取员工推广统计信息' })
  async getPromotionStatistics(@Param('employeeId') employeeId: string) {
    return await this.service.getPromotionStatistics(parseInt(employeeId));
  }

  @Get('/', { summary: '管理端查询推广关系列表' })
  async index(@Query() query: any) {
    const { current, pageSize, employeeId, keyword, startTime, endTime } =
      query;

    return await this.service.findPromotionRelations({
      current: current ? parseInt(current) : undefined,
      pageSize: pageSize ? parseInt(pageSize) : undefined,
      employeeId: employeeId ? parseInt(employeeId) : undefined,
      keyword,
      startTime,
      endTime,
    });
  }
}
