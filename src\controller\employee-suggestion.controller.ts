import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComplaintService } from '../service/complaint.service';
import {
  CreateEmployeeSuggestionDto,
  UpdateEmployeeSuggestionDto,
  QueryEmployeeSuggestionDto,
} from '../dto/complaint.dto';
import { CustomError } from '../error/custom.error';

@Controller('/employee-suggestions')
export class EmployeeSuggestionController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComplaintService;

  @Get('/:employeeId', { summary: '获取员工建议列表' })
  async index(
    @Param('employeeId') employeeId: number,
    @Query() query: QueryEmployeeSuggestionDto
  ) {
    const { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      keyword,
      startDate,
      endDate,
      subCategory,
      status,
    } = query;
    void o;
    void l;

    let page = 1;
    let size = 10;

    if (current && pageSize) {
      page = current;
      size = pageSize;
    } else if (offset !== undefined && limit !== undefined) {
      page = Math.floor(offset / limit) + 1;
      size = limit;
    }

    const filters = {
      subCategory,
      status,
      keyword,
      startDate,
      endDate,
    };

    return await this.service.getEmployeeSuggestions(
      employeeId,
      filters,
      page,
      size
    );
  }

  @Get('/:employeeId/:id', { summary: '按ID查询员工建议' })
  async show(@Param('employeeId') employeeId: number, @Param('id') id: number) {
    const res = await this.service.getEmployeeSuggestionWithRelations(id);
    if (!res) {
      throw new CustomError('未找到指定建议');
    }

    // 验证建议是否属于该员工
    if (res.employeeId !== employeeId) {
      throw new CustomError('建议不属于该员工', 403);
    }

    return res;
  }

  @Post('/:employeeId', { summary: '员工创建建议' })
  async create(
    @Param('employeeId') employeeId: number,
    @Body() createDto: CreateEmployeeSuggestionDto
  ) {
    if (!employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }

    return await this.service.createEmployeeSuggestion(employeeId, createDto);
  }

  @Put('/:employeeId/:id', { summary: '员工更新建议' })
  async update(
    @Param('employeeId') employeeId: number,
    @Param('id') id: number,
    @Body() updateDto: UpdateEmployeeSuggestionDto
  ) {
    if (!employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }

    return await this.service.updateEmployeeSuggestion(
      employeeId,
      id,
      updateDto
    );
  }

  @Del('/:employeeId/:id', { summary: '员工删除建议' })
  async delete(
    @Param('employeeId') employeeId: number,
    @Param('id') id: number
  ) {
    if (!employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }

    return await this.service.deleteEmployeeSuggestion(employeeId, id);
  }
}
