import { Controller, Get, Inject, Param } from '@midwayjs/core';
import { CustomerService } from '../service/customer.service';
import { CustomError } from '../error/custom.error';

@Controller('/customer-addresses')
export class CustomerAddressesController {
  @Inject()
  customerService: CustomerService;

  @Get('/customer/:customerId', { summary: '查询指定用户的地址列表' })
  async getCustomerAddresses(@Param('customerId') customerId: number) {
    // 验证客户是否存在
    const customer = await this.customerService.findById(customerId);
    if (!customer) {
      throw new CustomError('客户不存在');
    }

    // 获取客户地址列表
    return await this.customerService.getAddressList(customerId);
  }
}
