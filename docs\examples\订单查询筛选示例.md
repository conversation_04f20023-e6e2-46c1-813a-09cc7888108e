# 订单查询筛选功能使用示例

## 概述

`GET /orders` 接口现已支持多种筛选条件，包括时间范围筛选、用户名称模糊查找、服务人员模糊查找和服务大类型筛选。

## 使用示例

### 1. 基础分页查询
```http
GET /orders?current=1&pageSize=10
```

### 2. 按时间范围筛选
```http
# 查询2024年1月的订单
GET /orders?startTime=2024-01-01T00:00:00.000Z&endTime=2024-01-31T23:59:59.999Z

# 查询指定日期之后的订单
GET /orders?startTime=2024-07-01T00:00:00.000Z

# 查询指定日期之前的订单
GET /orders?endTime=2024-07-31T23:59:59.999Z
```

### 3. 按用户信息筛选
```http
# 按客户手机号精确查找
GET /orders?phone=13800138000

# 按客户昵称模糊查找
GET /orders?customerName=张三

# 组合查询：手机号和昵称
GET /orders?phone=13800138000&customerName=张三
```

### 4. 按服务人员筛选
```http
# 按员工姓名模糊查找
GET /orders?employeename=李师傅

# 查找姓"王"的员工的订单
GET /orders?employeename=王
```

### 5. 按服务类型筛选
```http
# 查询美容类服务订单（根据 ServiceType.type 字段筛选）
GET /orders?serviceType=美容

# 查询洗护类服务订单（根据 ServiceType.type 字段筛选）
GET /orders?serviceType=洗护

# 查询其他服务类型（具体值取决于数据库中 service_types 表的 type 字段值）
GET /orders?serviceType=护理
```

### 6. 组合筛选示例
```http
# 查询2024年7月张三的美容订单
GET /orders?startTime=2024-07-01T00:00:00.000Z&endTime=2024-07-31T23:59:59.999Z&customerName=张三&serviceType=美容

# 查询李师傅在本月的洗护订单
GET /orders?startTime=2024-07-01T00:00:00.000Z&employeename=李师傅&serviceType=洗护&current=1&pageSize=20

# 查询特定手机号用户的已完成订单
GET /orders?phone=13800138000&status=已完成,已评价
```

### 7. 包含追加服务详情
```http
# 查询订单并包含追加服务详情
GET /orders?includeAdditionalServices=true&current=1&pageSize=10
```

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |
| phone | string | 否 | 客户手机号精确匹配 |
| customerName | string | 否 | 客户昵称模糊匹配 |
| employeename | string | 否 | 员工姓名模糊匹配 |
| serviceType | string | 否 | 服务大类型筛选 |
| startTime | string | 否 | 开始时间（ISO格式） |
| endTime | string | 否 | 结束时间（ISO格式） |
| status | string | 否 | 订单状态筛选，多个状态用逗号分隔 |
| includeAdditionalServices | string | 否 | 是否包含追加服务详情，传'true'启用 |

## 注意事项

1. **时间格式**: 时间参数需要使用ISO 8601格式，如：`2024-07-16T10:30:00.000Z`
2. **模糊查询**: `customerName` 和 `employeename` 支持模糊匹配，会自动添加通配符
3. **服务类型**: `serviceType` 参数对应 ServiceType 实体中的 `type` 字段，查询时会筛选该字段值匹配的服务类型
4. **组合查询**: 所有筛选条件可以任意组合使用，系统会自动构建AND查询条件
5. **性能考虑**: 当使用 `includeAdditionalServices=true` 时，查询会包含更多关联数据，可能影响性能

## 常见服务类型

`serviceType` 参数的可用值取决于数据库中 `service_types` 表的 `type` 字段值。常见的服务类型包括：
- `美容`: 美容护理类服务
- `洗护`: 清洁洗护类服务
- 其他类型请查询 ServiceType 表中的 `type` 字段获取完整列表

**查询可用服务类型的SQL**:
```sql
SELECT DISTINCT type FROM service_types ORDER BY type;
```

## 返回数据结构

查询结果包含以下字段：
```json
{
  "list": [
    {
      "id": 1,
      "sn": "ORDER123456",
      "customerId": 1,
      "employeeId": 1,
      "status": "已完成",
      "orderTime": "2024-07-16T10:30:00.000Z",
      "serviceTime": "2024-07-16T14:00:00.000Z",
      "totalFee": 100.00,
      "customer": {
        "id": 1,
        "nickname": "张三",
        "phone": "13800138000"
      },
      "employee": {
        "id": 1,
        "name": "李师傅"
      },
      "orderDetails": [
        {
          "id": 1,
          "serviceName": "基础洗护",
          "servicePrice": 80.00,
          "service": {
            "id": 1,
            "serviceName": "基础洗护",
            "serviceType": {
              "id": 1,
              "name": "洗护服务",
              "type": "洗护"
            }
          }
        }
      ]
    }
  ],
  "total": 1
}
```
