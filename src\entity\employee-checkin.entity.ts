import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Employee } from './employee.entity';

/** 出车拍照分组照片接口 */
export interface CheckInPhotos {
  /** 车辆外观照片，最多9张 */
  vehicleExterior?: string[];
  /** 服务人员照片，最多9张 */
  serviceStaff?: string[];
  /** 车内情况照片，最多9张 */
  vehicleInterior?: string[];
}

export interface EmployeeCheckInAttributes {
  /** 打卡记录ID */
  id: number;
  /** 员工ID */
  employeeId: number;
  /** 分组照片对象 */
  photos: CheckInPhotos;
  /** 打卡描述 */
  description?: string;
  /** 打卡地址 */
  address?: string;
  /** 经度 */
  longitude?: number;
  /** 纬度 */
  latitude?: number;
  /** 打卡时间 */
  checkInTime: Date;
  /** 关联的员工信息 */
  employee?: Employee;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'employee_checkins',
  timestamps: true,
  comment: '员工出车打卡记录表',
})
export class EmployeeCheckIn
  extends Model<EmployeeCheckInAttributes>
  implements EmployeeCheckInAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '打卡记录ID',
  })
  id: number;

  @ForeignKey(() => Employee)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '员工ID',
  })
  employeeId: number;

  @Column({
    type: DataType.JSON,
    allowNull: false,
    comment:
      '分组照片对象，包含车辆外观、服务人员、车内情况三个分组，每组最多9张',
  })
  photos: CheckInPhotos;

  @Column({
    type: DataType.STRING(500),
    allowNull: true,
    comment: '打卡描述',
  })
  description?: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '打卡地址',
  })
  address?: string;

  @Column({
    type: DataType.DECIMAL(10, 7),
    allowNull: true,
    comment: '经度',
  })
  longitude?: number;

  @Column({
    type: DataType.DECIMAL(10, 7),
    allowNull: true,
    comment: '纬度',
  })
  latitude?: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '打卡时间',
  })
  checkInTime: Date;

  @BelongsTo(() => Employee)
  employee: Employee;
}
