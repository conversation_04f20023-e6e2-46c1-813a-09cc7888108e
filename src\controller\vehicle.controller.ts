import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { VehicleService } from '../service/vehicle.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Controller('/vehicles')
export class VehicleController {
  @Inject()
  ctx: Context;

  @Inject()
  service: VehicleService;

  @Get('/', { summary: '查询车辆列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // plateNumber支持模糊查询
    if (queryInfo.plateNumber) {
      queryInfo.plateNumber = {
        [Op.like]: `%${queryInfo.plateNumber}%`,
      };
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: ['employee'],
    });
  }

  @Get('/:id', { summary: '按ID查询车辆' })
  async show(@Param('id') id: string) {
    const res = await this.service.getVehicleDetail(Number(id));
    if (!res) {
      throw new CustomError('未找到指定车辆');
    }
    return res;
  }

  @Post('/', { summary: '新增车辆' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新车辆' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除车辆' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Put('/:id/location', { summary: '更新车辆位置' })
  async updateLocation(
    @Param('id') id: number,
    @Body() { latitude, longitude }: { latitude: number; longitude: number }
  ) {
    await this.service.updateLocation(id, latitude, longitude);
    return true;
  }

  @Put('/:id/status', { summary: '更新车辆状态' })
  async updateStatus(
    @Param('id') id: number,
    @Body() { status }: { status: string }
  ) {
    await this.service.updateStatus(id, status);
    return true;
  }

  @Get('/:id/employee', { summary: '获取车辆关联的员工' })
  async getEmployee(@Param('id') id: number) {
    return await this.service.getEmployee(id);
  }
}
