import { Provide, Inject, ILogger } from '@midwayjs/core';
import { MessageService } from '../service/sys-message.service';
import { MESSAGE_CONSTANTS } from '../common/Constant';

/**
 * 消息助手类 - 提供便捷的消息发送方法
 */
@Provide()
export class MessageHelper {
  @Inject()
  messageService: MessageService;

  @Inject()
  logger: ILogger;

  /**
   * 格式化时间为 yyyy-MM-dd hh:mm:ss 格式
   * @param date 日期对象或日期字符串
   * @returns 格式化后的时间字符串
   */
  private formatDateTime(date: Date | string): string {
    const d = new Date(date);
    if (isNaN(d.getTime())) {
      return String(date); // 如果无法解析，返回原始值
    }

    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 发送新订单通知
   */
  async sendNewOrderNotification(
    userId: number,
    orderData: {
      orderNo: string;
      customerName: string;
      serviceTime?: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_NEW,
        userId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `订单新消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送订单接单通知
   */
  async sendOrderAcceptedNotification(
    userId: number,
    orderData: {
      orderNo: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_ACCEPTED,
        userId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `订单接单消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送订单完成通知
   */
  async sendOrderCompletedNotification(
    userId: number,
    orderData: {
      orderNo: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_COMPLETED,
        userId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `订单完成消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送系统维护通知
   */
  async sendSystemMaintenanceNotification(
    userId: number,
    maintenanceData: {
      maintenanceTime: string;
      duration: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.SYSTEM_MAINTENANCE,
        userId,
        maintenanceData
      );
    } catch (error) {
      this.logger.error(
        `系统维护消息通知发送失败：${JSON.stringify(maintenanceData)}`,
        error
      );
    }
  }

  /**
   * 发送平台政策更新通知
   */
  async sendPlatformPolicyNotification(userId: number) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.PLATFORM_POLICY,
        userId,
        {}
      );
    } catch (error) {
      this.logger.error(`平台政策更新消息通知发送失败: ${userId}`, error);
    }
  }

  /**
   * 发送自定义消息
   */
  async sendCustomMessage(
    userId: number,
    type: 'system' | 'platform' | 'order',
    title: string,
    content: string,
    extraData?: any
  ) {
    try {
      await this.messageService.createMessage({
        userId,
        type,
        title,
        content,
        extraData,
      });
    } catch (error) {
      this.logger.error(
        `发送自定义消息失败：${JSON.stringify({
          userId,
          type,
          title,
          content,
          extraData,
        })}`,
        error
      );
    }
  }

  /**
   * 批量发送消息给多个用户
   */
  async sendBatchMessage(
    userIds: number[],
    type: 'system' | 'platform' | 'order',
    title: string,
    content: string,
    extraData?: any
  ) {
    const promises = userIds.map(userId =>
      this.sendCustomMessage(userId, type, title, content, extraData)
    );
    return await Promise.all(promises);
  }

  // ==================== 用户端消息通知方法 ====================

  /**
   * 发送订单确认通知（用户端）
   */
  async sendOrderConfirmedNotification(
    customerId: number,
    orderData: {
      orderNo: string;
      totalAmount: string;
      serviceTime?: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_CONFIRMED,
        customerId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `订单确认消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送员工接单通知（用户端）
   */
  async sendOrderEmployeeAssignedNotification(
    customerId: number,
    orderData: {
      orderNo: string;
      employeeName: string;
      employeePhone: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_EMPLOYEE_ASSIGNED,
        customerId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `员工接单消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送员工出发通知（用户端）
   */
  async sendOrderDispatchedNotification(
    customerId: number,
    orderData: {
      orderNo: string;
      employeeName: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_DISPATCHED,
        customerId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `员工出发消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送服务开始通知（用户端）
   */
  async sendOrderStartedNotification(
    customerId: number,
    orderData: {
      orderNo: string;
      serviceName: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_STARTED,
        customerId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `服务开始消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送服务完成通知（用户端）
   */
  async sendOrderFinishedNotification(
    customerId: number,
    orderData: {
      orderNo: string;
      serviceName: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_FINISHED,
        customerId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `服务完成消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送订单取消通知（用户端）
   */
  async sendOrderCancelledNotification(
    customerId: number,
    orderData: {
      orderNo: string;
      reason?: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_CANCELLED,
        customerId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `订单取消消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送订单退款通知（用户端）
   */
  async sendOrderRefundedNotification(
    customerId: number,
    orderData: {
      orderNo: string;
      refundAmount: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_REFUNDED,
        customerId,
        orderData
      );
    } catch (error) {
      this.logger.error(
        `订单退款消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 发送服务时间修改通知（用户端）
   */
  async sendOrderTimeChangedNotification(
    customerId: number,
    orderData: {
      orderNo: string;
      newServiceTime: string;
      modifier: string;
    }
  ) {
    try {
      await this.messageService.createMessageFromTemplate(
        MESSAGE_CONSTANTS.templateCodes.ORDER_TIME_CHANGED,
        customerId,
        {
          ...orderData,
          newServiceTime: this.formatDateTime(orderData.newServiceTime),
        }
      );
    } catch (error) {
      this.logger.error(
        `服务时间修改消息通知发送失败：订单${orderData.orderNo}`,
        error
      );
    }
  }

  /**
   * 通过模板批量发送消息
   */
  async sendBatchMessageFromTemplate(
    userIds: number[],
    templateCode: string,
    variables: Record<string, any> = {}
  ) {
    const promises = userIds.map(userId =>
      this.messageService.createMessageFromTemplate(
        templateCode,
        userId,
        variables
      )
    );
    return await Promise.all(promises);
  }
}
