import { Inject, Controller, Post, Get, Body } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { JwtService } from '@midwayjs/jwt';
import { User } from '../entity/user.entity';
import { CustomError } from '../error/custom.error';
import * as bcrypt from 'bcryptjs';
import { Role } from '../entity';
import { FeatureService } from '../service/feature.service';
import { TokenPayload } from '../interface';

@Controller('/auth')
export class AuthController {
  @Inject()
  ctx: Context;

  @Inject()
  jwtService: JwtService;

  @Inject()
  featureService: FeatureService;

  @Post('/login', { summary: '用户登录' })
  async login(
    @Body() { username, password }: { username: string; password: string }
  ) {
    // 查找用户
    const user = await User.findOne({
      where: { username, isActive: true },
      include: ['roles'], // 加载角色信息
    });

    if (!user) {
      throw new CustomError('用户名或密码错误');
    }

    // 验证密码
    const valid = await bcrypt.compare(password, user.password);
    if (!valid) {
      throw new CustomError('用户名或密码错误');
    }

    // 生成访问 token
    const accessPayload = {
      userId: user.id,
      username: user.username,
      roles: user.roles?.map(role => role.name) || [],
      type: 'access',
      userType: 'admin', // 管理员登录
    };
    const token = this.jwtService.signSync(accessPayload, { expiresIn: '2h' });

    // 生成刷新 token
    const refreshPayload = {
      userId: user.id,
      type: 'refresh',
    };
    const refreshToken = this.jwtService.signSync(refreshPayload, {
      expiresIn: '7d',
    });

    return {
      token,
      refreshToken,
      user: {
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        roles: user.roles,
      },
    };
  }

  @Post('/refresh', { summary: '刷新访问令牌' })
  async refresh(@Body() { refreshToken }: { refreshToken: string }) {
    try {
      // 验证刷新令牌
      const decoded = (await this.jwtService.verify(
        refreshToken
      )) as unknown as TokenPayload;

      // 检查是否是刷新token
      if (decoded.type !== 'refresh') {
        throw new CustomError('无效的刷新令牌');
      }

      // 查找用户
      const user = await User.findByPk(decoded.userId, {
        include: ['roles'],
      });

      if (!user) {
        throw new CustomError('用户不存在');
      }

      // 生成新的访问令牌
      const accessPayload: TokenPayload = {
        userId: user.id,
        username: user.username,
        roles: user.roles?.map(role => role.name) || [],
        type: 'access',
        userType: 'admin', // 管理员刷新token
      };
      const newToken = this.jwtService.signSync(accessPayload);

      return {
        token: newToken,
      };
    } catch (error) {
      throw new CustomError('刷新令牌无效或已过期');
    }
  }

  @Get('/currentUser', { summary: '获取当前登录用户信息' })
  async getUserInfo() {
    const user = await User.findByPk(this.ctx.state.user.userId, {
      attributes: { exclude: ['password'] },
      include: [Role],
    });

    const userInfo = user.toJSON();
    const { roles } = userInfo;
    const features = await this.featureService.getListByRoles(
      roles.map(item => item.id)
    );

    if (!user) {
      throw new CustomError('用户不存在');
    }

    return {
      ...userInfo,
      features,
    };
  }

  @Post('/logout', { summary: '退出登录' })
  async logout() {
    // JWT 不需要服务端处理登出,前端清除 token 即可
    return { message: '退出成功' };
  }
}
