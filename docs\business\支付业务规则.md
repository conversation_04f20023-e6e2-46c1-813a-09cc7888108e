# 支付业务规则

## 支付流程

### 基本支付流程
1. **订单创建**: 用户创建订单，计算应付金额
2. **发起支付**: 调用微信支付接口发起支付
3. **用户支付**: 用户在微信中完成支付
4. **支付回调**: 微信服务器回调通知支付结果
5. **状态更新**: 更新订单状态为已支付

### 0元订单处理
- **自动跳过**: 0元订单自动跳过支付环节
- **状态变更**: 直接从"待付款"变为"待接单"
- **记录标记**: 系统自动标记为已付款

## 支付方式

### 微信支付
- **小程序支付**: 主要支付方式
- **支付金额**: 以分为单位
- **支付描述**: 包含订单信息和服务描述
- **回调处理**: 处理微信支付回调

### 其他支付方式
- **余额支付**: 使用账户余额支付
- **组合支付**: 余额+微信支付组合
- **优惠抵扣**: 权益卡、代金券抵扣

## 价格计算规则

### 基础价格计算
```
原价 = 所有服务项目价格之和
```

### 权益卡抵扣
- **折扣卡**: 按折扣率计算抵扣金额
- **次卡**: 按次数使用，不计算金额抵扣
- **抵扣公式**: `抵扣金额 = 原价 × 折扣率`

### 代金券抵扣
- **满减条件**: 必须满足最低使用金额
- **抵扣上限**: 不能超过代金券面额
- **抵扣公式**: `抵扣金额 = min(代金券面额, 符合条件的金额)`

### 最终价格
```
最终价格 = 原价 - 权益卡抵扣 - 代金券抵扣
最终价格 = max(0, 最终价格)  // 不能为负数
```

## 退款规则

### 退款条件
- **全额退款**: 服务未开始的订单
- **部分退款**: 服务部分完成的订单
- **不可退款**: 服务已完成的订单

### 退款流程
1. **申请退款**: 用户或管理员申请退款
2. **审核退款**: 管理员审核退款申请
3. **执行退款**: 调用微信退款接口
4. **退款回调**: 处理微信退款回调
5. **状态更新**: 更新订单状态

### 退款金额计算
- **主订单退款**: 退还用户实际支付金额
- **追加服务退款**: 按追加服务金额退款
- **优惠券处理**: 全额退款时退还，部分退款时不退还

## 优惠券规则

### 权益卡规则
- **折扣卡**: 必须填写折扣率，有效期可选
- **次卡**: 必须填写可用次数，折扣率强制为0
- **使用限制**: 同一订单只能使用一张权益卡
- **过期处理**: 过期或次数用完自动失效

### 代金券规则
- **使用条件**: 满足最低使用金额
- **有效期**: 必须在有效期内使用
- **使用限制**: 同一订单只能使用一张代金券
- **优先级**: 优先使用即将过期的代金券

## 支付异常处理

### 微信支付成功但本地更新失败
- **问题描述**: 微信支付成功，但本地数据库更新失败
- **处理方案**: 
  1. 提供手动同步接口
  2. 定期同步微信支付状态
  3. 记录异常日志便于排查
- **防护措施**: 防止用户重复支付

### 重复退款防护
- **检查机制**: 退款前检查订单状态
- **防重复**: 防止同一订单重复退款
- **记录保留**: 保留退款操作记录

### 支付超时处理
- **超时时间**: 设置支付超时时间（如30分钟）
- **自动取消**: 超时未支付的订单自动取消
- **库存释放**: 释放相关资源和库存

## 数据库事务

### 复杂操作事务
- **订单创建**: 创建订单、扣减库存、记录日志
- **支付成功**: 更新订单状态、更新会员信息、记录支付
- **退款操作**: 更新订单状态、退还优惠券、记录退款

### 外部调用处理
- **微信支付**: 处理微信支付接口调用异常
- **回调处理**: 确保回调处理的幂等性
- **数据一致性**: 确保本地数据与微信数据一致

## 支付安全

### 签名验证
- **请求签名**: 验证微信回调的签名
- **参数校验**: 校验回调参数的完整性
- **防篡改**: 防止支付金额等关键信息被篡改

### 风险控制
- **异常监控**: 监控异常支付行为
- **限额控制**: 设置单笔和日累计限额
- **黑名单**: 维护风险用户黑名单

## 对账机制

### 日常对账
- **自动对账**: 定期与微信支付平台对账
- **差异处理**: 处理对账差异
- **报表生成**: 生成对账报表

### 数据核查
- **支付数据**: 核查支付数据的准确性
- **退款数据**: 核查退款数据的完整性
- **财务报表**: 生成财务相关报表

## 性能优化

### 支付性能
- **接口优化**: 优化支付接口的响应时间
- **缓存策略**: 缓存支付配置信息
- **异步处理**: 异步处理非关键支付流程

### 数据库优化
- **索引设计**: 在支付相关字段上建立索引
- **分表策略**: 大数据量支付表考虑分表
- **读写分离**: 支付查询和更新操作分离

## 监控告警

### 支付监控
- **支付成功率**: 监控支付成功率
- **支付时长**: 监控支付处理时长
- **异常支付**: 监控异常支付情况

### 退款监控
- **退款率**: 监控退款率变化
- **退款时效**: 监控退款处理时效
- **退款异常**: 监控退款异常情况

### 告警机制
- **实时告警**: 支付异常实时告警
- **阈值告警**: 关键指标超过阈值告警
- **日报周报**: 定期生成支付相关报告

## 财务管理

### 收入统计
- **日收入**: 统计每日收入情况
- **月收入**: 统计每月收入情况
- **服务收入**: 按服务类型统计收入

### 成本分析
- **平台成本**: 微信支付手续费等
- **运营成本**: 人工、设备等成本
- **利润分析**: 收入与成本的利润分析

### 财务报表
- **收支明细**: 详细的收支明细报表
- **利润报表**: 利润情况报表
- **税务报表**: 税务相关报表

## 合规要求

### 支付合规
- **资质要求**: 确保支付资质合规
- **数据保护**: 保护用户支付信息
- **监管要求**: 满足监管部门要求

### 财务合规
- **账务处理**: 规范的账务处理流程
- **发票管理**: 电子发票的开具和管理
- **税务申报**: 按时进行税务申报
