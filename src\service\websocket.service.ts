import { Provide, App, <PERSON><PERSON>, <PERSON>ope, ScopeEnum } from '@midwayjs/core';
import { Application } from '@midwayjs/ws';
import { ILogger } from '@midwayjs/logger';

/**
 * 简单的 WebSocket 服务类
 */
@Provide()
@Scope(ScopeEnum.Singleton)
export class WebSocketService {
  @App('webSocket')
  wsApp: Application;

  @Logger()
  logger: ILogger;

  /**
   * 向所有连接的客户端广播消息
   */
  async broadcastToAll(message: any) {
    const messageStr = JSON.stringify(message);
    let clientCount = 0;
    let errorCount = 0;

    // 创建一个客户端数组的副本，避免在遍历过程中集合被修改
    const clients = Array.from(this.wsApp.clients);

    for (const client of clients) {
      try {
        // 检查连接状态：1 = OPEN
        if (client.readyState === 1) {
          client.send(messageStr);
          clientCount++;
        } else if (client.readyState === 3) {
          // 3 = CLOSED，从集合中移除已关闭的连接
          this.wsApp.clients.delete(client);
        }
      } catch (error) {
        errorCount++;
        this.logger.warn('向客户端发送消息失败:', error.message);
        // 发送失败的客户端可能已经断开连接，尝试从集合中移除
        try {
          this.wsApp.clients.delete(client);
        } catch (deleteError) {
          // 忽略删除错误
        }
      }
    }

    this.logger.info(
      `广播消息给 ${clientCount} 个客户端${
        errorCount > 0 ? `，${errorCount} 个客户端发送失败` : ''
      }`
    );
    return clientCount;
  }

  /**
   * 获取当前连接的客户端数量
   */
  getConnectedClientCount(): number {
    let count = 0;
    const clients = Array.from(this.wsApp.clients);

    for (const client of clients) {
      try {
        if (client.readyState === 1) {
          count++;
        } else if (client.readyState === 3) {
          // 清理已关闭的连接
          this.wsApp.clients.delete(client);
        }
      } catch (error) {
        // 忽略错误，继续统计其他客户端
        this.logger.warn('检查客户端连接状态时出错:', error.message);
      }
    }
    return count;
  }

  /**
   * 清理已断开的连接
   */
  cleanupDisconnectedClients(): number {
    let cleanedCount = 0;
    const clients = Array.from(this.wsApp.clients);

    for (const client of clients) {
      try {
        // 检查连接状态，移除已关闭的连接
        if (client.readyState === 2 || client.readyState === 3) {
          // 2 = CLOSING, 3 = CLOSED
          this.wsApp.clients.delete(client);
          cleanedCount++;
        }
      } catch (error) {
        // 如果检查状态时出错，也认为是无效连接，移除它
        this.wsApp.clients.delete(client);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.info(`清理了 ${cleanedCount} 个断开的连接`);
    }

    return cleanedCount;
  }

  /**
   * 测试广播功能
   */
  async testBroadcast(): Promise<number> {
    const testMessage = {
      type: 'test',
      message: '测试广播消息',
      timestamp: Date.now(),
    };

    return await this.broadcastToAll(testMessage);
  }
}
