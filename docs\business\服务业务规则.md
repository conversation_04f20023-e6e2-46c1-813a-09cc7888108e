# 服务业务规则

## 服务管理规则

### 服务状态
- **启用状态**: 正常提供的服务，用户可以选择
- **禁用状态**: 暂停提供的服务，用户不可选择
- **查询规则**: 默认只显示启用状态的服务

### 服务信息
- **基本信息**: 服务名称、描述、价格、服务类型
- **时长信息**: 平均服务时长（取最近10次记录的平均值）
- **权益信息**: 是否支持权益卡折扣

### 业务数据处理
- **现有订单**: 已有业务数据始终显示关联的服务，不受状态影响
- **新订单**: 只能选择启用状态的服务
- **数据一致性**: 确保历史数据的完整性

## 服务时长管理

### 时长计算规则
- **统计范围**: 取最近10次服务记录
- **计算方法**: 计算平均时长并更新到服务表
- **更新时机**: 每次服务完成后自动更新

### 计时规则
- **主服务**: 开始整体服务时自动开始所有主服务计时
- **追加服务**: 需要员工手动开始和结束计时
- **并发服务**: 允许同时进行多个服务项目的计时

### 时长统计
- **手动更新**: 提供手动更新所有服务平均时长的功能
- **单独更新**: 支持更新单个服务的平均时长
- **数据修复**: 提供时长数据修复工具

## 照片墙管理

### 照片展示规则
- **展示条件**: 只展示启用状态的照片
- **排序方式**: 支持最新排序和热门排序（按点赞数）
- **优先级**: 管理员可设置照片展示优先级

### 照片互动
- **点赞功能**: 用户可以点赞和取消点赞
- **浏览统计**: 自动统计照片浏览次数
- **热门算法**: 根据点赞数和浏览数计算热门度

### 照片管理
- **自动生成**: 服务完成后自动生成照片墙记录
- **手动添加**: 管理员可手动添加照片墙记录
- **批量操作**: 支持批量启用、禁用、删除操作

## 活动管理规则

### 发布规则
- **唯一性**: 同一受众（用户端/员工端）同时只能有一个活动发布
- **自动处理**: 发布新活动时自动取消同受众的其他已发布活动
- **状态检查**: 只有启用状态的活动才能发布

### 活动内容
- **内容类型**: 支持富文本内容和外部链接
- **封面图片**: 必须设置活动封面图片
- **受众设置**: 区分用户端和员工端活动

### 活动生命周期
- **创建**: 创建活动，默认为未发布状态
- **发布**: 发布活动，用户可见
- **取消发布**: 取消发布，用户不可见
- **删除**: 只能删除未发布的活动

## 服务照片管理

### 照片分类
- **服务前照片**: 服务开始前拍摄的照片
- **服务后照片**: 服务完成后拍摄的照片
- **分类管理**: 按照片类型分类管理

### 上传规则
- **数量限制**: 每类照片最多9张
- **格式要求**: 支持常见图片格式
- **大小限制**: 单张图片不超过10MB

### 照片处理
- **自动压缩**: 上传时自动压缩图片
- **去重处理**: 实现照片去重逻辑
- **批量上传**: 支持批量上传和删除

## 服务类型管理

### 类型分类
- **洗护服务**: 基础清洁护理服务
- **美容服务**: 造型美容服务
- **增值服务**: 额外的增值服务

### 权限控制
- **职位限制**: 根据员工职位限制可提供的服务类型
- **服务匹配**: 确保员工职位与服务类型匹配

## 价格管理

### 价格策略
- **基础价格**: 服务的基础价格
- **动态定价**: 根据时间、地区等因素调整价格
- **优惠政策**: 权益卡、代金券等优惠政策

### 价格计算
- **原价计算**: 所有服务项目价格之和
- **折扣计算**: 根据权益卡类型计算折扣
- **最终价格**: 原价减去各种优惠后的最终价格

## 服务质量管理

### 质量标准
- **服务标准**: 制定各项服务的质量标准
- **操作规范**: 详细的服务操作规范
- **质量检查**: 定期进行服务质量检查

### 评价体系
- **客户评价**: 客户对服务的评价和反馈
- **评分统计**: 统计各项服务的平均评分
- **改进措施**: 根据评价制定改进措施

## 数据一致性

### 服务状态同步
- **实时更新**: 服务状态变更实时同步
- **缓存更新**: 及时更新相关缓存数据
- **前端同步**: 确保前端显示的服务状态正确

### 关联数据处理
- **订单关联**: 处理服务与订单的关联关系
- **照片关联**: 处理服务与照片的关联关系
- **评价关联**: 处理服务与评价的关联关系

## 特殊场景处理

### 服务下架
- **影响评估**: 评估服务下架对现有订单的影响
- **数据保留**: 保留历史数据用于分析
- **用户通知**: 通知相关用户服务变更

### 价格调整
- **生效时间**: 价格调整的生效时间
- **订单影响**: 处理价格调整对现有订单的影响
- **通知机制**: 通知用户价格变更

### 系统维护
- **服务暂停**: 系统维护时的服务暂停处理
- **数据备份**: 重要数据的备份和恢复
- **故障恢复**: 系统故障时的快速恢复机制

## 性能优化

### 查询优化
- **服务列表**: 优化服务列表查询性能
- **照片加载**: 优化照片墙的加载性能
- **活动查询**: 优化活动查询性能

### 缓存策略
- **服务信息**: 缓存常用的服务信息
- **照片数据**: 缓存热门照片数据
- **活动内容**: 缓存当前发布的活动内容

### 数据库优化
- **索引设计**: 在常用查询字段上建立索引
- **分页查询**: 大数据量查询使用分页
- **读写分离**: 查询和更新操作分离

## 监控告警

### 业务监控
- **服务使用率**: 监控各项服务的使用情况
- **照片互动**: 监控照片墙的互动情况
- **活动效果**: 监控活动的参与效果

### 系统监控
- **服务状态**: 监控服务的可用状态
- **照片上传**: 监控照片上传是否正常
- **数据同步**: 监控数据同步状态

### 异常处理
- **服务异常**: 处理服务相关的异常情况
- **数据异常**: 处理数据不一致等异常
- **用户反馈**: 及时处理用户反馈的问题
