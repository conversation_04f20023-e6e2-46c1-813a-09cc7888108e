import { createRedisStore } from '@midwayjs/cache-manager';
import { MidwayConfig } from '@midwayjs/core';

export default {
  // use for cookie sign key, should change to your own and keep security
  keys: '1735182119043_9267',
  koa: {
    port: 3001,
  },
  webSocket: {
    // WebSocket 与 Koa 共享端口
    enableServerHeartbeatCheck: true,
    serverHeartbeatInterval: 30000,
  },
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'Aa@123456',
        database: 'pet_manager',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_bin',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
        logging: false,
      },
    },
  },
  cacheManager: {
    clients: {
      dictionary: {
        store: createRedisStore('dictionary'),
        options: {
          ttl: 0,
        },
      },
    },
  },
  redis: {
    clients: {
      default: {
        port: 6379,
        host: '127.0.0.1',
        password: 'Aa@123456',
        db: 0,
      },
      dictionary: {
        port: 6379,
        host: '127.0.0.1',
        password: 'Aa@123456',
        db: 1,
      },
    },
  },
  // JWT配置
  jwt: {
    secret: 'petAuth@2025',
    expiresIn: '2h',
  },
  token: {
    secret: 'oauth@2025',
    expiresIn: 2 * 3600,
    refreshExpiresIn: 15 * 24 * 3600,
  },
  i18n: {
    localeTable: {
      zh_CN: {
        'is required': '{{field}} 是必填项',
        'invalid format': '{{field}} 格式不正确',
      },
    },
  },
  cos: {
    secretId: 'AKIDqa7ThljbLuGTWSDl63oMl380kJm6CC3k', // 固定密钥
    secretKey: 'XsGYmpmncR9fy63qZ5OTQDqGsKgtuhHy', // 固定密钥
    proxy: '',
    host: 'sts.tencentcloudapi.com', // 域名，非必须，默认为 sts.tencentcloudapi.com
    // endpoint: 'sts.internal.tencentcloudapi.com', // 域名，非必须，与host二选一，默认为 sts.tencentcloudapi.com
    durationSeconds: 1800, // 密钥有效期
    // 放行判断相关参数
    bucket: 'pet-1301580990', // 换成你的 bucket
    region: 'ap-guangzhou', // 换成 bucket 所在地区
    // allowPrefix: 'exampleobject', // 这里改成允许的路径前缀，可以根据自己网站的用户登录态判断允许上传的具体路径，例子： a.jpg 或者 a/* 或者 * (使用通配符*存在重大安全风险, 请谨慎评估使用)
  },
  zos: {
    baseURL: 'https://zos-global.ctapi.ctyun.cn',
    bucket: 'pet',
    regionID: '200000001851',
    ak: 'c88cf001278b445282a0c3bd5820fda1',
    sk: '3404e94d1bac412c881e6c2cafa34e24',
  },
  axios: {
    default: {
      // 所有实例复用的配置
      timeout: 10000, // default is `0` (no timeout)
    },
    clients: {
      // 默认实例的配置
      default: {
        // `withCredentials` indicates whether or not cross-site Access-Control requests
        // should be made using credentials
        // withCredentials: false, // default
      },
      weapp: {
        baseURL: 'https://api.weixin.qq.com',
        headers: {
          Accept: 'application/json, text/plain, */*',
          'Content-Type': 'application/json',
        },
      },
      weapp_pay: {
        baseURL: 'https://api.mch.weixin.qq.com',
      },
    },
  },
  // 微信小程序配置,贝宠约洗
  weapp: {
    appid: 'wxf41f43ceacd58114',
    secret: '6b8a6ddcc2c6c0f4e417a9427c1e2d2c',
    sym_sn: '',
    sym_key: 'xogkwc2q+LlhEGS8mWcszSWiGmjh0hPouqUCm04nYOA=',
    templateIds: {
      orderConfirmation: 'LSBwIETgaGdTBEOSE2jWc9H4kSqV6kmaLbYubaj98Gk', // 接单成功通知模板ID
      orderStatusChange: 'aSWstKk_cnoJDW9g9NMlbuW1cHCt_VV2zfwjRIePDqA', // 服务进度提醒模板ID
      orderTimeChange: '93CbZXFuxibQs9HeYo0u2tZSj-lLyPj6CZK73878N98', // 服务时间更改通知模板ID
      orderCompletion: 'AvjwdSi9u2BhNTpwBSghM8tnaXwoDfHTY-xBg-rQ3WI', // 服务完成通知模板ID
    },
  },
  // 微信小程序配置,员工端
  weapp_employee: {
    appid: 'wxb59303831dd1ccac',
    secret: '9cb9bb8fbc1f5983d9b8192df5ceaf94',
    sym_sn: '',
  },
  // 微信支付配置，商户模式，注册不是服务商模式
  wePay: {
    sp_mchid: '1699183760',
    sp_appid: 'wxf41f43ceacd58114',
    pem_sn: '61857F342165D8E83A351159999A08C0E5FEF77B',
  },
} as MidwayConfig;
