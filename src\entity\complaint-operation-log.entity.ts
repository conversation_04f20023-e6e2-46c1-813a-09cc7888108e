import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Complaint } from './complaint.entity';
import { Customer } from './customer.entity';
import { Employee } from './employee.entity';

export interface ComplaintOperationLogAttributes {
  /** 操作记录ID */
  id: number;
  /** 关联投诉建议ID */
  complaintId: number;
  /** 操作类型 */
  operationType:
    | 'create'
    | 'update'
    | 'status_change'
    | 'handle'
    | 'reply'
    | 'delete';
  /** 操作人类型 */
  operatorType: 'customer' | 'employee' | 'admin' | 'system';
  /** 操作人ID */
  operatorId?: number;
  /** 操作人姓名（冗余存储） */
  operatorName?: string;
  /** 操作前状态 */
  beforeStatus?: string;
  /** 操作后状态 */
  afterStatus?: string;
  /** 操作描述 */
  description: string;
  /** 操作详情（JSON格式存储变更内容） */
  operationDetails?: string;
  /** 操作时间 */
  operatedAt: Date;
  /** 创建时间 */
  createdAt?: Date;
  /** 关联的投诉建议 */
  complaint?: Complaint;
  /** 关联的客户（当操作人是客户时） */
  customer?: Customer;
  /** 关联的员工（当操作人是员工时） */
  employee?: Employee;
}

@Table({
  tableName: 'complaint_operation_logs',
  timestamps: true,
  updatedAt: false,
  comment: '投诉建议操作记录表',
})
export class ComplaintOperationLog
  extends Model<ComplaintOperationLogAttributes>
  implements ComplaintOperationLogAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '操作记录ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联投诉建议ID',
  })
  @ForeignKey(() => Complaint)
  complaintId: number;

  @Column({
    type: DataType.ENUM(
      'create',
      'update',
      'status_change',
      'handle',
      'reply',
      'delete'
    ),
    allowNull: false,
    comment:
      '操作类型：create-创建，update-更新，status_change-状态变更，handle-处理，reply-回复，delete-删除',
  })
  operationType:
    | 'create'
    | 'update'
    | 'status_change'
    | 'handle'
    | 'reply'
    | 'delete';

  @Column({
    type: DataType.ENUM('customer', 'employee', 'admin', 'system'),
    allowNull: false,
    comment:
      '操作人类型：customer-客户，employee-员工，admin-管理员，system-系统',
  })
  operatorType: 'customer' | 'employee' | 'admin' | 'system';

  @Column({
    type: DataType.INTEGER,
    comment: '操作人ID',
  })
  operatorId?: number;

  @Column({
    type: DataType.STRING(100),
    comment: '操作人姓名（冗余存储）',
  })
  operatorName?: string;

  @Column({
    type: DataType.STRING(50),
    comment: '操作前状态',
  })
  beforeStatus?: string;

  @Column({
    type: DataType.STRING(50),
    comment: '操作后状态',
  })
  afterStatus?: string;

  @Column({
    type: DataType.STRING(500),
    allowNull: false,
    comment: '操作描述',
  })
  description: string;

  @Column({
    type: DataType.TEXT,
    comment: '操作详情（JSON格式存储变更内容）',
  })
  operationDetails?: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '操作时间',
  })
  operatedAt: Date;

  @BelongsTo(() => Complaint, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  complaint: Complaint;

  @BelongsTo(() => Customer, {
    foreignKey: 'operatorId',
    constraints: false,
  })
  customer: Customer;

  @BelongsTo(() => Employee, {
    foreignKey: 'operatorId',
    constraints: false,
  })
  employee: Employee;
}
