import { Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { OrderSpecialNote, Order, Employee } from '../entity';
import { CustomError } from '../error/custom.error';

export interface CreateOrderSpecialNoteData {
  orderId: number;
  employeeId: number;
  content: string;
  photos?: string[];
}

export interface UpdateOrderSpecialNoteData {
  content?: string;
  photos?: string[];
}

@Provide()
export class OrderSpecialNoteService extends BaseService<OrderSpecialNote> {
  constructor() {
    super('订单特殊情况说明');
  }

  getModel() {
    return OrderSpecialNote;
  }

  /**
   * 创建订单特殊情况说明
   */
  async createSpecialNote(data: CreateOrderSpecialNoteData) {
    const { orderId, employeeId, content, photos } = data;

    // 验证订单是否存在
    const order = await Order.findByPk(orderId);
    if (!order) {
      throw new CustomError('订单不存在', 404);
    }

    // 验证员工是否存在
    const employee = await Employee.findByPk(employeeId);
    if (!employee) {
      throw new CustomError('员工不存在', 404);
    }

    // 验证员工是否是该订单的服务员工
    if (order.employeeId !== employeeId) {
      throw new CustomError('只有订单的服务员工才能添加特殊情况说明', 403);
    }

    // 验证照片数量
    if (photos && photos.length > 9) {
      throw new CustomError('最多只能上传9张照片', 400);
    }

    // 检查是否已存在特殊情况说明
    const existingNote = await OrderSpecialNote.findOne({
      where: { orderId, employeeId },
    });

    if (existingNote) {
      throw new CustomError('该订单已存在特殊情况说明，请使用更新接口', 400);
    }

    // 使用事务创建特殊情况说明并更新订单状态
    const result = await OrderSpecialNote.sequelize.transaction(async (t) => {
      // 创建特殊情况说明
      const specialNote = await OrderSpecialNote.create({
        orderId,
        employeeId,
        content,
        photos: photos || [],
      }, { transaction: t });

      // 更新订单的hasSpecialNote字段
      await order.update({ hasSpecialNote: true }, { transaction: t });

      return specialNote;
    });

    return await this.findById(result.id);
  }

  /**
   * 更新订单特殊情况说明
   */
  async updateSpecialNote(
    orderId: number,
    employeeId: number,
    data: UpdateOrderSpecialNoteData
  ) {
    // 查找特殊情况说明
    const specialNote = await OrderSpecialNote.findOne({
      where: { orderId, employeeId },
    });

    if (!specialNote) {
      throw new CustomError('特殊情况说明不存在', 404);
    }

    // 验证照片数量
    if (data.photos && data.photos.length > 9) {
      throw new CustomError('最多只能上传9张照片', 400);
    }

    // 更新数据
    const updateData: any = {};
    if (data.content !== undefined) {
      updateData.content = data.content;
    }
    if (data.photos !== undefined) {
      updateData.photos = data.photos;
    }

    await specialNote.update(updateData);

    return await this.findById(specialNote.id);
  }

  /**
   * 获取订单特殊情况说明
   */
  async getSpecialNoteByOrderId(orderId: number) {
    return await OrderSpecialNote.findOne({
      where: { orderId },
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });
  }

  /**
   * 删除订单特殊情况说明
   */
  async deleteSpecialNote(orderId: number, employeeId: number) {
    const specialNote = await OrderSpecialNote.findOne({
      where: { orderId, employeeId },
    });

    if (!specialNote) {
      throw new CustomError('特殊情况说明不存在', 404);
    }

    // 使用事务删除特殊情况说明并更新订单状态
    await OrderSpecialNote.sequelize.transaction(async (t) => {
      // 删除特殊情况说明
      await specialNote.destroy({ transaction: t });

      // 检查该订单是否还有其他特殊情况说明
      const remainingNotes = await OrderSpecialNote.count({
        where: { orderId },
        transaction: t,
      });

      // 如果没有其他特殊情况说明，更新订单的hasSpecialNote字段
      if (remainingNotes === 0) {
        await Order.update(
          { hasSpecialNote: false },
          { where: { id: orderId }, transaction: t }
        );
      }
    });

    return true;
  }

  /**
   * 按ID查询特殊情况说明（包含关联信息）
   */
  async findById(id: number) {
    return await OrderSpecialNote.findByPk(id, {
      include: [
        {
          model: Order,
          attributes: ['id', 'sn', 'status'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });
  }

  /**
   * 管理端查询所有特殊情况说明
   */
  async findAllForAdmin(
    current = 1,
    pageSize = 10,
    orderId?: number,
    employeeId?: number,
    keyword?: string
  ) {
    const offset = (current - 1) * pageSize;
    const where: any = {};

    if (orderId) {
      where.orderId = orderId;
    }

    if (employeeId) {
      where.employeeId = employeeId;
    }

    const include: any[] = [
      {
        model: Order,
        attributes: ['id', 'sn', 'status', 'customerId'],
        where: keyword
          ? {
              sn: {
                [require('sequelize').Op.like]: `%${keyword}%`,
              },
            }
          : undefined,
      },
      {
        model: Employee,
        attributes: ['id', 'name', 'phone'],
      },
    ];

    const { rows: list, count: total } = await OrderSpecialNote.findAndCountAll({
      where,
      include,
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });

    return {
      list,
      total,
      current,
      pageSize,
    };
  }

  /**
   * 同步订单的hasSpecialNote字段
   * 用于数据修复，确保订单表的hasSpecialNote字段与实际的特殊情况说明记录一致
   */
  async syncOrderHasSpecialNoteField() {
    // 获取所有有特殊情况说明的订单ID
    const ordersWithNotes = await OrderSpecialNote.findAll({
      attributes: ['orderId'],
      group: ['orderId'],
      raw: true,
    });

    const orderIdsWithNotes = ordersWithNotes.map(item => item.orderId);

    // 使用事务更新订单表
    await Order.sequelize.transaction(async (t) => {
      // 将所有订单的hasSpecialNote设为false
      await Order.update(
        { hasSpecialNote: false },
        { where: {}, transaction: t }
      );

      // 将有特殊情况说明的订单的hasSpecialNote设为true
      if (orderIdsWithNotes.length > 0) {
        await Order.update(
          { hasSpecialNote: true },
          {
            where: { id: orderIdsWithNotes },
            transaction: t
          }
        );
      }
    });

    return {
      totalOrders: await Order.count(),
      ordersWithSpecialNotes: orderIdsWithNotes.length,
      message: '订单hasSpecialNote字段同步完成'
    };
  }
}
