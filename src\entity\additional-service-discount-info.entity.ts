import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { AdditionalServiceOrder, DiscountType } from './';

/**
 * 追加服务优惠信息属性接口
 */
export interface AdditionalServiceDiscountInfoAttributes {
  /** 优惠信息ID */
  id: number;
  /** 关联追加服务订单ID */
  additionalServiceOrderId: number;
  /** 优惠类型：coupon-代金券，membership_card-权益卡 */
  discountType: DiscountType;
  /** 优惠ID：代金券ID或权益卡ID */
  discountId: number;
  /** 优惠金额 */
  discountAmount: number;
  /** 是否已退回 */
  isRefunded: boolean;
  /** 退回时间 */
  refundTime?: Date;
  /** 退回描述 */
  refundDescription?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 关联追加服务订单 */
  additionalServiceOrder?: AdditionalServiceOrder;
}

/**
 * 追加服务优惠信息表
 */
@Table({
  tableName: 'additional_service_discount_infos',
  timestamps: true,
  comment: '追加服务优惠信息表',
})
export class AdditionalServiceDiscountInfo
  extends Model<AdditionalServiceDiscountInfoAttributes>
  implements AdditionalServiceDiscountInfoAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '优惠信息ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联追加服务订单ID',
  })
  @ForeignKey(() => AdditionalServiceOrder)
  additionalServiceOrderId: number;

  @Column({
    type: DataType.ENUM(...Object.values(DiscountType)),
    allowNull: false,
    comment: '优惠类型：coupon-代金券，membership_card-权益卡',
  })
  discountType: DiscountType;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '优惠ID：代金券ID或权益卡ID',
  })
  discountId: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '优惠金额',
  })
  discountAmount: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '是否已退回',
  })
  isRefunded: boolean;

  @Column({
    type: DataType.DATE,
    comment: '退回时间',
  })
  refundTime?: Date;

  @Column({
    type: DataType.STRING(255),
    comment: '退回描述',
  })
  refundDescription?: string;

  @BelongsTo(() => AdditionalServiceOrder, { onDelete: 'CASCADE' })
  additionalServiceOrder?: AdditionalServiceOrder;
}
