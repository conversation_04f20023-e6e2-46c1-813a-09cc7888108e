import {
  Controller,
  Get,
  Inject,
  Param,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { WepayService } from '../../service/wepay.service';

@Controller('/admin/wepay')
export class WepayAdminController {
  @Inject()
  ctx: Context;

  @Inject()
  wepayService: WepayService;

  @Get('/transactions/sn/:sn', { summary: '管理端查询微信支付状态' })
  async getTransactionStatus(@Param('sn') sn: string) {
    this.ctx.logger.info('【管理端查询微信支付状态】：', { sn });

    try {
      // 调用带状态同步的查询方法
      const wechatResult = await this.wepayService.getTransactionsBySNWithStatusSync(sn);
      
      return {
        ...wechatResult,
        localSyncResult: {
          synced: true,
          message: '本地订单状态已检查并同步',
        },
      };
    } catch (error) {
      this.ctx.logger.error('【管理端查询微信支付状态失败】：', {
        sn,
        error: error.message,
      });

      return {
        out_trade_no: sn,
        error: error.message,
        localSyncResult: {
          synced: false,
          message: `查询失败：${error.message}`,
        },
      };
    }
  }
}
