import { Controller, Get, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { BannerService } from '../../service/banner.service';

@Controller('/openapi/banners')
export class BannerOpenApiController {
  @Inject()
  ctx: Context;

  @Inject()
  bannerService: BannerService;

  @Get('/', { summary: '小程序端获取轮播图列表' })
  async getBannerList() {
    this.ctx.logger.info('【小程序端获取轮播图列表】');

    const { list: banners } = await this.bannerService.findEnabled();

    // 只返回必要的字段给小程序端
    return banners.map(banner => ({
      id: banner.id,
      imageURL: banner.imageURL,
      jumpType: banner.jumpType,
      jumpLink: banner.jumpLink,
      priority: banner.priority,
    }));
  }
}
