import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Customer } from './customer.entity';
import { MembershipCardType } from './membership-card-type.entity';

export interface CustomerMembershipCardAttributes {
  /** 用户权益卡唯一标识 */
  id: number;
  /** 关联用户ID */
  customerId: number;
  /** 关联权益卡类型ID */
  cardTypeId: number;
  /** 购买时间 */
  purchaseTime: Date;
  /** 到期时间，null表示无到期时间限制 */
  expiryTime?: Date;
  /** 剩余使用次数，-1表示不限次数 */
  remainTimes: number;
  /** 状态：active-有效，expired-已过期，used-已用完 */
  status: 'active' | 'expired' | 'used';
  /** 关联的用户信息 */
  customer?: Customer;
  /** 关联的权益卡类型信息 */
  cardType?: MembershipCardType;
}

@Table({
  tableName: 'customer_membership_cards',
  timestamps: true,
  comment: '客户权益卡表',
})
export class CustomerMembershipCard
  extends Model<CustomerMembershipCardAttributes>
  implements CustomerMembershipCardAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '客户卡唯一标识',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联客户',
  })
  @ForeignKey(() => Customer)
  customerId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联卡类型',
  })
  @ForeignKey(() => MembershipCardType)
  cardTypeId: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '剩余使用次数，-1表示不限次数',
  })
  remainTimes: number;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW,
    comment: '购买时间',
  })
  purchaseTime: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '到期时间，null表示无到期时间限制',
  })
  expiryTime?: Date;

  @Column({
    type: DataType.ENUM('active', 'expired', 'used'),
    allowNull: false,
    defaultValue: 'active',
    comment: '状态：active-有效，expired-已过期，used-已用完',
  })
  status: 'active' | 'expired' | 'used';

  @BelongsTo(() => Customer)
  customer?: Customer;

  @BelongsTo(() => MembershipCardType, {
    // 已经分配的卡类型不能删除
    onDelete: 'NO ACTION',
  })
  cardType?: MembershipCardType;
}
