import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { BannerService } from '../service/banner.service';
import { CosService } from '../service/cos.service';
import { CustomError } from '../error/custom.error';
import {
  CreateBannerDto,
  UpdateBannerDto,
  QueryBannerDto,
  UpdateBannerPriorityDto,
} from '../dto/banner.dto';

@Controller('/banners')
export class BannerController {
  @Inject()
  ctx: Context;

  @Inject()
  service: BannerService;

  @Inject()
  cosService: CosService;

  @Get('/', { summary: '查询轮播图列表' })
  async index(@Query() query: QueryBannerDto) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [
        ['priority', 'DESC'],
        ['createdAt', 'DESC'],
      ],
    });
  }

  @Get('/:id', { summary: '按ID查询轮播图' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定轮播图');
    }
    return res;
  }

  @Post('/', { summary: '新增轮播图' })
  async create(@Body() info: CreateBannerDto) {
    // 验证跳转类型和相关字段
    this.validateJumpFields(info);
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新轮播图' })
  async update(@Param('id') id: string, @Body() body: UpdateBannerDto) {
    // 验证跳转类型和相关字段
    if (body.jumpType !== undefined) {
      this.validateJumpFields(body);
    }
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除轮播图' })
  async destroy(@Param('id') id: string) {
    // 先查询轮播图信息，获取图片链接
    const banner = await this.service.findById(id);
    if (!banner) {
      throw new CustomError('未找到指定轮播图');
    }

    // 删除轮播图记录
    await this.service.delete({ id });

    // 不能删除，因为存在与其他模块共用图片的情况
    // 同步删除对应的图片
    // if (banner.imageURL) {
    //   try {
    //     const key = banner.imageURL?.split('myqcloud.com/')[1];
    //     if (key) {
    //       await this.cosService.deleteObject(key);
    //     }
    //   } catch (error) {
    //     // 图片删除失败不影响轮播图删除，只记录日志
    //     this.ctx.logger.error('删除轮播图图片失败:', error);
    //   }
    // }

    return true;
  }

  @Put('/:id/priority', { summary: '更新轮播图优先级' })
  async updatePriority(
    @Param('id') id: number,
    @Body() body: UpdateBannerPriorityDto
  ) {
    await this.service.updatePriority(id, body.priority);
    return true;
  }

  @Get('/enabled/list', { summary: '获取已启用的轮播图列表' })
  async findEnabled() {
    return await this.service.findEnabled();
  }

  /**
   * 验证跳转类型和相关字段
   */
  private validateJumpFields(data: any) {
    const { jumpType, jumpLink } = data;

    if (jumpType === 'custom') {
      // 自定义链接类型，必须填写jumpLink
      if (!jumpLink) {
        throw new CustomError('跳转类型为自定义链接时，跳转链接不能为空');
      }
    } else if (jumpType === 'activity') {
      // 活动页类型，清空jumpLink
      data.jumpLink = null;
    } else if (jumpType === null || jumpType === undefined) {
      // 无跳转类型，清空jumpLink
      data.jumpLink = null;
    }
  }
}
