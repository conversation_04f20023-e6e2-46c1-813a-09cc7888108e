import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 投诉建议统计查询基础DTO
 */
export class ComplaintStatisticsBaseDto {
  @Rule(RuleType.string().optional().error(new Error('开始日期格式错误')))
  startDate?: string;

  @Rule(RuleType.string().optional().error(new Error('结束日期格式错误')))
  endDate?: string;
}

/**
 * 投诉建议概览统计DTO
 */
export class ComplaintOverviewDto extends ComplaintStatisticsBaseDto {}

/**
 * 投诉建议趋势统计DTO
 */
export class ComplaintTrendDto extends ComplaintStatisticsBaseDto {
  @Rule(
    RuleType.string()
      .valid('day', 'week', 'month')
      .optional()
      .default('day')
      .error(new Error('时间周期必须是day、week或month'))
  )
  periodType?: 'day' | 'week' | 'month';
}

/**
 * 客户投诉建议统计DTO
 */
export class CustomerComplaintStatisticsDto extends ComplaintStatisticsBaseDto {
  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .error(new Error('页码必须是大于0的整数'))
  )
  page?: number;

  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(20)
      .error(new Error('每页数量必须在1-100之间'))
  )
  pageSize?: number;

  @Rule(
    RuleType.string()
      .valid('complaintCount', 'suggestionCount', 'totalCount')
      .optional()
      .default('totalCount')
      .error(
        new Error('排序字段必须是complaintCount、suggestionCount或totalCount')
      )
  )
  sortBy?: 'complaintCount' | 'suggestionCount' | 'totalCount';

  @Rule(
    RuleType.string()
      .valid('asc', 'desc')
      .optional()
      .default('desc')
      .error(new Error('排序方向必须是asc或desc'))
  )
  sortOrder?: 'asc' | 'desc';
}

/**
 * 员工投诉统计DTO
 */
export class EmployeeComplaintStatisticsDto extends ComplaintStatisticsBaseDto {
  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .error(new Error('页码必须是大于0的整数'))
  )
  page?: number;

  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(20)
      .error(new Error('每页数量必须在1-100之间'))
  )
  pageSize?: number;

  @Rule(
    RuleType.string()
      .valid('complaintCount', 'resolvedCount', 'resolveRate')
      .optional()
      .default('complaintCount')
      .error(
        new Error('排序字段必须是complaintCount、resolvedCount或resolveRate')
      )
  )
  sortBy?: 'complaintCount' | 'resolvedCount' | 'resolveRate';

  @Rule(
    RuleType.string()
      .valid('asc', 'desc')
      .optional()
      .default('desc')
      .error(new Error('排序方向必须是asc或desc'))
  )
  sortOrder?: 'asc' | 'desc';
}

/**
 * 处理效率统计DTO
 */
export class ProcessingEfficiencyDto extends ComplaintStatisticsBaseDto {}

/**
 * 热点问题分析DTO
 */
export class HotIssuesAnalysisDto extends ComplaintStatisticsBaseDto {
  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .max(50)
      .optional()
      .default(10)
      .error(new Error('返回数量必须在1-50之间'))
  )
  limit?: number;
}

/**
 * 投诉建议状态分布DTO
 */
export class ComplaintStatusDistributionDto extends ComplaintStatisticsBaseDto {
  @Rule(
    RuleType.string()
      .valid('complaint', 'suggestion')
      .optional()
      .error(new Error('类型必须是complaint或suggestion'))
  )
  category?: 'complaint' | 'suggestion';

  @Rule(
    RuleType.string()
      .valid('order', 'employee', 'platform', 'service')
      .optional()
      .error(new Error('子类型必须是order、employee、platform或service'))
  )
  subCategory?: 'order' | 'employee' | 'platform' | 'service';
}

/**
 * 投诉建议处理人员统计DTO
 */
export class HandlerStatisticsDto extends ComplaintStatisticsBaseDto {
  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .error(new Error('页码必须是大于0的整数'))
  )
  page?: number;

  @Rule(
    RuleType.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(20)
      .error(new Error('每页数量必须在1-100之间'))
  )
  pageSize?: number;

  @Rule(
    RuleType.string()
      .valid('handledCount', 'avgProcessingHours', 'resolveRate')
      .optional()
      .default('handledCount')
      .error(
        new Error('排序字段必须是handledCount、avgProcessingHours或resolveRate')
      )
  )
  sortBy?: 'handledCount' | 'avgProcessingHours' | 'resolveRate';

  @Rule(
    RuleType.string()
      .valid('asc', 'desc')
      .optional()
      .default('desc')
      .error(new Error('排序方向必须是asc或desc'))
  )
  sortOrder?: 'asc' | 'desc';
}

/**
 * 投诉建议满意度统计DTO
 */
export class ComplaintSatisfactionDto extends ComplaintStatisticsBaseDto {
  @Rule(
    RuleType.string()
      .valid('complaint', 'suggestion')
      .optional()
      .error(new Error('类型必须是complaint或suggestion'))
  )
  category?: 'complaint' | 'suggestion';

  @Rule(
    RuleType.string()
      .valid('order', 'employee', 'platform', 'service')
      .optional()
      .error(new Error('子类型必须是order、employee、platform或service'))
  )
  subCategory?: 'order' | 'employee' | 'platform' | 'service';
}

/**
 * 投诉建议时间分布统计DTO
 */
export class ComplaintTimeDistributionDto extends ComplaintStatisticsBaseDto {
  @Rule(
    RuleType.string()
      .valid('hour', 'weekday', 'month')
      .optional()
      .default('hour')
      .error(new Error('时间类型必须是hour、weekday或month'))
  )
  timeType?: 'hour' | 'weekday' | 'month';
}
