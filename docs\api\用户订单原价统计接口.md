# 用户订单原价统计接口

## 1. 获取用户有效订单原价统计

### 接口信息
- **路径**: `GET /order-statistics/customer-original-price`
- **描述**: 获取指定用户的有效订单总量和原价总金额统计
- **权限**: 需要登录认证

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| customerId | number | 是 | 用户ID |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

### 请求示例
```
GET /order-statistics/customer-original-price?customerId=1&startDate=2024-01-01&endDate=2024-12-31
```

### 响应数据
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "customerId": 1,
    "orderCount": 15,
    "mainOrderOriginalPrice": 2500.00,
    "mainOrderPaidAmount": 2200.00,
    "additionalServiceOriginalPrice": 800.00,
    "additionalServicePaidAmount": 720.00,
    "totalOriginalPrice": 3300.00,
    "totalPaidAmount": 2920.00,
    "totalCardDeduction": 300.00,
    "totalCouponDeduction": 80.00,
    "totalDeduction": 380.00
  }
}
```

### 响应字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| customerId | number | 用户ID |
| orderCount | number | 有效订单总数（已完成+已评价） |
| mainOrderOriginalPrice | number | 主订单原价总计 |
| mainOrderPaidAmount | number | 主订单实付总计 |
| additionalServiceOriginalPrice | number | 追加服务原价总计 |
| additionalServicePaidAmount | number | 追加服务实付总计 |
| totalOriginalPrice | number | 总原价（主订单+追加服务） |
| totalPaidAmount | number | 总实付（主订单+追加服务） |
| totalCardDeduction | number | 权益卡抵扣总计 |
| totalCouponDeduction | number | 代金券抵扣总计 |
| totalDeduction | number | 总抵扣金额 |

---

## 2. 获取用户有效订单列表

### 接口信息
- **路径**: `GET /order-statistics/customer-valid-orders`
- **描述**: 获取指定用户的有效订单详细列表，包含服务和增项服务信息
- **权限**: 需要登录认证

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| customerId | number | 是 | 用户ID |
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| sortBy | string | 否 | 排序字段：orderTime/serviceTime/originalPrice/totalFee，默认orderTime |
| sortOrder | string | 否 | 排序方式：asc/desc，默认desc |
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20，最大100 |

### 请求示例
```
GET /order-statistics/customer-valid-orders?customerId=1&current=1&pageSize=10&sortBy=orderTime&sortOrder=desc
```

### 响应数据
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 123,
        "sn": "ORD20240101001",
        "status": "已完成",
        "orderTime": "2024-01-01T10:00:00.000Z",
        "serviceTime": "2024-01-01T14:00:00.000Z",
        "address": "北京市朝阳区xxx",
        "addressDetail": "xxx小区1号楼",
        "originalPrice": 200.00,
        "totalFee": 180.00,
        "cardDeduction": 20.00,
        "couponDeduction": 0.00,
        "additionalServiceOriginalPrice": 50.00,
        "additionalServiceAmount": 45.00,
        "totalOriginalPrice": 250.00,
        "totalPaidAmount": 225.00,
        "mainServices": [
          {
            "id": 456,
            "serviceId": 10,
            "serviceName": "宠物洗护",
            "servicePrice": 200.00,
            "serviceType": "洗护",
            "serviceTypeName": "grooming",
            "petName": "小白",
            "petType": "狗",
            "petBreed": "金毛",
            "userRemark": "请轻柔一些",
            "additionalServices": [
              {
                "id": 5,
                "name": "指甲修剪",
                "type": "grooming",
                "price": 30.00
              }
            ]
          }
        ],
        "additionalServiceOrders": [
          {
            "id": 789,
            "sn": "ADD20240101001",
            "status": "paid",
            "originalPrice": 50.00,
            "totalFee": 45.00,
            "cardDeduction": 5.00,
            "couponDeduction": 0.00,
            "createdAt": "2024-01-01T15:00:00.000Z",
            "services": [
              {
                "id": 101,
                "serviceId": 15,
                "serviceName": "除虫服务",
                "servicePrice": 50.00,
                "quantity": 1,
                "serviceType": "医疗",
                "serviceTypeName": "medical"
              }
            ]
          }
        ]
      }
    ],
    "total": 15,
    "page": 1,
    "pageSize": 10
  }
}
```

### 响应字段说明

#### 订单基本信息
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | number | 订单ID |
| sn | string | 订单编号 |
| status | string | 订单状态 |
| orderTime | string | 下单时间 |
| serviceTime | string | 服务时间 |
| address | string | 服务地址 |
| addressDetail | string | 地址详情 |

#### 价格信息
| 字段名 | 类型 | 描述 |
|--------|------|------|
| originalPrice | number | 主订单原价 |
| totalFee | number | 主订单实付 |
| cardDeduction | number | 权益卡抵扣 |
| couponDeduction | number | 代金券抵扣 |
| additionalServiceOriginalPrice | number | 追加服务原价 |
| additionalServiceAmount | number | 追加服务实付 |
| totalOriginalPrice | number | 总原价 |
| totalPaidAmount | number | 总实付 |

#### 主服务信息 (mainServices)
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | number | 订单详情ID |
| serviceId | number | 服务ID |
| serviceName | string | 服务名称 |
| servicePrice | number | 服务价格 |
| serviceType | string | 服务类型名称 |
| serviceTypeName | string | 服务类型标识 |
| petName | string | 宠物名称 |
| petType | string | 宠物类型 |
| petBreed | string | 宠物品种 |
| userRemark | string | 用户备注 |
| additionalServices | array | 关联的增项服务 |

#### 追加服务订单 (additionalServiceOrders)
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | number | 追加服务订单ID |
| sn | string | 追加服务订单编号 |
| status | string | 订单状态 |
| originalPrice | number | 原价 |
| totalFee | number | 实付 |
| cardDeduction | number | 权益卡抵扣 |
| couponDeduction | number | 代金券抵扣 |
| createdAt | string | 创建时间 |
| services | array | 追加服务明细 |

### 注意事项
1. 只统计状态为"已完成"和"已评价"的订单
2. 原价包含主订单原价和追加服务原价
3. 实付金额包含主订单实付和追加服务实付
4. 服务类型和时间信息都会返回
5. 支持按多种字段排序和分页查询
