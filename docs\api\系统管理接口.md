# 系统管理API接口

## 1. 权限管理接口

### 1.1 角色管理
- **查询角色列表**: `GET /roles`
- **查询角色详情**: `GET /roles/{id}`
- **创建角色**: `POST /roles`
- **更新角色**: `PUT /roles/{id}`
- **删除角色**: `DELETE /roles/{id}`
- **添加权限**: `POST /roles/{id}/permissions`
- **移除权限**: `DELETE /roles/{id}/permissions`

### 1.2 权限管理
- **查询权限列表**: `GET /permissions`
- **查询权限详情**: `GET /permissions/{id}`
- **创建权限**: `POST /permissions`
- **更新权限**: `PUT /permissions/{id}`
- **删除权限**: `DELETE /permissions/{id}`

### 1.3 用户权限管理
- **查询用户角色**: `GET /users/{id}/roles`
- **分配角色**: `POST /users/{id}/roles`
- **移除角色**: `DELETE /users/{id}/roles`

## 2. 轮播图管理接口

### 2.1 查询轮播图列表
- **接口**: `GET /banners`
- **描述**: 查询轮播图列表，支持分页和筛选
- **参数**: 
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `status` (string): 状态筛选
  - `position` (string): 位置筛选

### 2.2 查询轮播图详情
- **接口**: `GET /banners/{id}`
- **描述**: 查询指定轮播图的详细信息
- **参数**: `id` (number): 轮播图ID

### 2.3 创建轮播图
- **接口**: `POST /banners`
- **描述**: 创建新轮播图
- **请求体**: 
  ```json
  {
    "title": "轮播图标题",
    "imageUrl": "https://example.com/banner.jpg",
    "linkUrl": "https://example.com/link",
    "position": "首页",
    "sortOrder": 1,
    "status": "enabled",
    "startTime": "2024-01-01T00:00:00Z",
    "endTime": "2024-12-31T23:59:59Z"
  }
  ```

### 2.4 更新轮播图
- **接口**: `PUT /banners/{id}`
- **描述**: 更新轮播图信息
- **参数**: `id` (number): 轮播图ID
- **请求体**: 包含需要更新的字段

### 2.5 删除轮播图
- **接口**: `DELETE /banners/{id}`
- **描述**: 删除轮播图
- **参数**: `id` (number): 轮播图ID

### 2.6 启用/禁用轮播图
- **接口**: `PUT /banners/{id}/toggle`
- **描述**: 启用或禁用轮播图
- **参数**: `id` (number): 轮播图ID

### 2.7 调整轮播图排序
- **接口**: `PUT /banners/{id}/sort`
- **描述**: 调整轮播图排序
- **参数**: `id` (number): 轮播图ID
- **请求体**: 
  ```json
  {
    "sortOrder": 5
  }
  ```

### 2.8 获取前端轮播图
- **接口**: `GET /openapi/banners`
- **描述**: 前端获取有效的轮播图列表
- **参数**: 
  - `position` (string): 位置筛选

## 3. 字典管理接口

### 3.1 查询字典列表
- **接口**: `GET /dictionaries`
- **描述**: 查询字典列表，支持分页和筛选
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - `type` (string): 字典类型筛选

### 3.2 查询字典详情
- **接口**: `GET /dictionaries/{id}`
- **描述**: 查询字典详情
- **参数**: `id` (number): 字典ID

### 3.3 创建字典
- **接口**: `POST /dictionaries`
- **描述**: 创建新字典项
- **请求体**: 
  ```json
  {
    "type": "员工职位",
    "code": "XI_HU_SHI",
    "name": "洗护师",
    "sortOrder": 1,
    "status": 1
  }
  ```

### 3.4 更新字典
- **接口**: `PUT /dictionaries/{id}`
- **描述**: 更新字典信息
- **参数**: `id` (number): 字典ID
- **请求体**: 包含需要更新的字段

### 3.5 删除字典
- **接口**: `DELETE /dictionaries/{id}`
- **描述**: 删除字典项
- **参数**: `id` (number): 字典ID

### 3.6 按类型查询字典
- **接口**: `GET /dictionaries/type/{type}`
- **描述**: 按类型查询字典项列表
- **参数**: `type` (string): 字典类型

## 4. 系统配置接口

### 4.1 查询系统配置
- **接口**: `GET /system-config`
- **描述**: 查询系统配置列表
- **参数**: 
  - `category` (string): 配置分类筛选

### 4.2 查询配置详情
- **接口**: `GET /system-config/{key}`
- **描述**: 查询指定配置项
- **参数**: `key` (string): 配置键名

### 4.3 更新系统配置
- **接口**: `PUT /system-config/{key}`
- **描述**: 更新系统配置
- **参数**: `key` (string): 配置键名
- **请求体**: 
  ```json
  {
    "value": "配置值",
    "description": "配置说明"
  }
  ```

### 4.4 批量更新配置
- **接口**: `POST /system-config/batch`
- **描述**: 批量更新系统配置
- **请求体**: 
  ```json
  {
    "configs": [
      {
        "key": "app_name",
        "value": "宠物服务管理系统"
      },
      {
        "key": "contact_phone",
        "value": "************"
      }
    ]
  }
  ```

## 5. 日志管理接口

### 5.1 查询操作日志
- **接口**: `GET /operation-logs`
- **描述**: 查询系统操作日志
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - `userId` (number): 用户ID筛选
  - `action` (string): 操作类型筛选
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

### 5.2 查询错误日志
- **接口**: `GET /error-logs`
- **描述**: 查询系统错误日志
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - `level` (string): 错误级别筛选
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

### 5.3 清理日志
- **接口**: `DELETE /logs/cleanup`
- **描述**: 清理过期日志
- **参数**: 
  - `days` (number): 保留天数

## 6. 数据统计接口

### 6.1 获取系统概览统计
- **接口**: `GET /statistics/overview`
- **描述**: 获取系统概览统计数据
- **参数**: 
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

### 6.2 获取订单统计
- **接口**: `GET /statistics/orders`
- **描述**: 获取订单相关统计数据
- **参数**: 
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期
  - `groupBy` (string): 分组方式（day/week/month）

### 6.3 获取客户统计
- **接口**: `GET /statistics/customers`
- **描述**: 获取客户相关统计数据
- **参数**: 
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

### 6.4 获取员工统计
- **接口**: `GET /statistics/employees`
- **描述**: 获取员工相关统计数据
- **参数**: 
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

### 6.5 获取收入统计
- **接口**: `GET /statistics/revenue`
- **描述**: 获取收入相关统计数据
- **参数**: 
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期
  - `groupBy` (string): 分组方式

## 7. 系统维护接口

### 7.1 数据备份
- **接口**: `POST /maintenance/backup`
- **描述**: 创建数据备份
- **参数**: 
  - `type` (string): 备份类型（full/incremental）

### 7.2 查询备份列表
- **接口**: `GET /maintenance/backups`
- **描述**: 查询备份文件列表
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量

### 7.3 恢复数据
- **接口**: `POST /maintenance/restore`
- **描述**: 从备份恢复数据
- **请求体**: 
  ```json
  {
    "backupId": "backup_20240101_001"
  }
  ```

### 7.4 清理缓存
- **接口**: `POST /maintenance/clear-cache`
- **描述**: 清理系统缓存
- **参数**: 
  - `type` (string): 缓存类型

### 7.5 系统健康检查
- **接口**: `GET /maintenance/health`
- **描述**: 系统健康状态检查
- **参数**: 无

## 8. 消息管理接口

### 8.1 查询系统消息
- **接口**: `GET /messages`
- **描述**: 查询系统消息列表
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - `type` (string): 消息类型筛选
  - `status` (string): 状态筛选

### 8.2 发送系统消息
- **接口**: `POST /messages`
- **描述**: 发送系统消息
- **请求体**: 
  ```json
  {
    "title": "系统通知",
    "content": "消息内容",
    "type": "system",
    "targetType": "all",
    "targetIds": [1, 2, 3]
  }
  ```

### 8.3 标记消息已读
- **接口**: `PUT /messages/{id}/read`
- **描述**: 标记消息为已读
- **参数**: `id` (number): 消息ID

### 8.4 删除消息
- **接口**: `DELETE /messages/{id}`
- **描述**: 删除消息
- **参数**: `id` (number): 消息ID

### 8.5 批量操作消息
- **接口**: `POST /messages/batch`
- **描述**: 批量操作消息（标记已读/删除）
- **请求体**: 
  ```json
  {
    "ids": [1, 2, 3],
    "action": "read"  // read/delete
  }
  ```

## 业务规则说明

### 权限管理规则
- 删除角色、功能或权限时，需要清除对应的授权信息
- 管理员拥有最大权限，可以操作任何数据

### 轮播图规则
- 支持按位置分类管理
- 可以设置生效时间范围
- 支持排序和启用/禁用状态

### 字典管理规则
- 按类型分组管理
- 支持排序和状态控制
- 删除时需要检查关联数据

### 系统配置规则
- 配置项按分类组织
- 支持批量更新
- 重要配置需要权限验证

### 日志管理规则
- 自动记录关键操作
- 支持按时间范围清理
- 错误日志分级管理
