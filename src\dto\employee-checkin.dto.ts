import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 出车拍照分组照片DTO
 */
export class CheckInPhotosDto {
  @Rule(RuleType.array().items(RuleType.string().uri()).max(9).optional())
  vehicleExterior?: string[];

  @Rule(RuleType.array().items(RuleType.string().uri()).max(9).optional())
  serviceStaff?: string[];

  @Rule(RuleType.array().items(RuleType.string().uri()).max(9).optional())
  vehicleInterior?: string[];
}

/**
 * 员工出车打卡DTO
 */
export class EmployeeCheckInDto {
  @Rule(RuleType.number().integer().positive().required())
  employeeId: number;

  @Rule(RuleType.object().required())
  photos: CheckInPhotosDto;

  @Rule(RuleType.string().max(500).optional())
  description?: string;

  @Rule(RuleType.string().max(255).optional())
  address?: string;

  @Rule(RuleType.number().optional())
  longitude?: number;

  @Rule(RuleType.number().optional())
  latitude?: number;
}
