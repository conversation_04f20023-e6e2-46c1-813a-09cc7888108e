import { Provide } from '@midwayjs/core';
import { PhotoWall } from '../entity/photo-wall.entity';
import { BaseService } from '../common/BaseService';
import { Order } from '../entity/order.entity';
import { ServicePhoto } from '../entity/service-photo.entity';
import { CustomError } from '../error/custom.error';
import { Customer } from '../entity/customer.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Pet } from '../entity/pet.entity';
import { Service } from '../entity/service.entity';
import { ServiceType } from '../entity/service-type.entity';
import { Op } from 'sequelize';

@Provide()
export class PhotoWallService extends BaseService<PhotoWall> {
  constructor() {
    super('照片墙');
  }

  getModel() {
    return PhotoWall;
  }

  /**
   * 根据订单ID查找照片墙记录
   */
  async findByOrder(orderId: number) {
    return await this.findOne({
      where: { orderId },
      include: [Order],
    });
  }

  /**
   * 获取最新照片列表
   */
  async findLatest(limit = 10) {
    return await this.findAll({
      query: { isEnabled: true },
      limit,
      order: [
        ['priority', 'DESC'],
        ['createdAt', 'DESC'],
      ],
      include: [Order],
    });
  }

  /**
   * 获取热门照片列表（按点赞数排序）
   */
  async findPopular(limit = 10) {
    return await this.findAll({
      query: { isEnabled: true },
      limit,
      order: [
        ['likeCount', 'DESC'],
        ['viewCount', 'DESC'],
        ['createdAt', 'DESC'],
      ],
      include: [Order],
    });
  }

  /**
   * 从服务照片创建照片墙记录
   */
  async createFromServicePhoto(
    orderId: number,
    data: {
      title?: string;
      description?: string;
      beforePhoto: string;
      afterPhoto: string;
      priority?: number;
    }
  ) {
    // 检查是否已存在该订单的照片墙记录
    const existing = await this.findByOrder(orderId);
    if (existing) {
      throw new CustomError('该订单已存在照片墙记录');
    }

    // 获取订单信息
    const order = await Order.findByPk(orderId, {
      include: [
        'customer',
        'orderDetails',
        'orderDetails.pet',
        'orderDetails.service',
        'orderDetails.service.serviceType',
      ],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 构建照片墙数据
    const photoWallData = {
      orderId,
      employeeId: order.employeeId,
      customerId: order.customerId,
      beforePhoto: data.beforePhoto,
      afterPhoto: data.afterPhoto,
      title:
        data.title ||
        `${order.orderDetails?.[0]?.petName || '宠物'}的${
          order.orderDetails?.[0]?.service?.serviceType?.name || '服务'
        }`,
      description: data.description || '',
      serviceTypeName:
        order.orderDetails?.[0]?.service?.serviceType?.name || '',
      petName: order.orderDetails?.[0]?.petName || '',
      petType: order.orderDetails?.[0]?.petType || '',
      priority: data.priority || 0,
      isEnabled: true,
      likeCount: 0,
      viewCount: 0,
    };

    return await this.create(photoWallData);
  }

  /**
   * 增加浏览数
   */
  async incrementViewCount(id: number) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({
      viewCount: photoWall.viewCount + 1,
    });

    return photoWall;
  }

  /**
   * 增加点赞数
   */
  async incrementLikeCount(id: number) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({
      likeCount: photoWall.likeCount + 1,
    });

    return photoWall;
  }

  /**
   * 减少点赞数
   */
  async decrementLikeCount(id: number) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({
      likeCount: Math.max(0, photoWall.likeCount - 1),
    });

    return photoWall;
  }

  /**
   * 启用/禁用照片展示
   */
  async toggleEnabled(id: number, isEnabled: boolean) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({ isEnabled });
    return photoWall;
  }

  /**
   * 设置展示优先级
   */
  async setPriority(id: number, priority: number) {
    const photoWall = await this.findById(id);
    if (!photoWall) {
      throw new CustomError('照片墙记录不存在');
    }

    await photoWall.update({ priority });
    return photoWall;
  }

  /**
   * 获取照片墙统计信息
   */
  async getStatistics() {
    const total = await PhotoWall.count();
    const enabled = await PhotoWall.count({ where: { isEnabled: true } });
    const totalViews = await PhotoWall.sum('viewCount');
    const totalLikes = await PhotoWall.sum('likeCount');

    return {
      total,
      enabled,
      disabled: total - enabled,
      totalViews: totalViews || 0,
      totalLikes: totalLikes || 0,
    };
  }

  /**
   * 获取可用于照片墙的服务照片列表
   */
  async getAvailableServicePhotos(query: {
    current?: number;
    pageSize?: number;
    employeeId?: number;
    customerId?: number;
    serviceTypeName?: string;
    hasBeforePhoto?: boolean;
    hasAfterPhoto?: boolean;
    excludeUsed?: boolean; // 是否排除已用于照片墙的照片
  }) {
    const {
      current = 1,
      pageSize = 10,
      excludeUsed = true,
      ...filters
    } = query;

    // 构建查询条件
    const whereConditions: any = {};

    if (filters.employeeId) {
      whereConditions.employeeId = filters.employeeId;
    }

    // 如果需要排除已使用的照片
    let excludeOrderIds: number[] = [];
    if (excludeUsed) {
      const usedPhotos = await PhotoWall.findAll({
        attributes: ['orderId'],
        where: { orderId: { [Op.ne]: null } },
      });
      excludeOrderIds = usedPhotos
        .map(p => p.orderId)
        .filter(id => id !== null);
    }

    if (excludeOrderIds.length > 0) {
      whereConditions.orderId = { [Op.notIn]: excludeOrderIds };
    }

    // 查询服务照片，不分页先获取所有数据进行过滤
    const servicePhotos = await ServicePhoto.findAll({
      where: whereConditions,
      include: [
        {
          model: Order,
          as: 'order',
          include: [
            { model: Customer, as: 'customer' },
            {
              model: OrderDetail,
              as: 'orderDetails',
              include: [
                { model: Pet, as: 'pet' },
                {
                  model: Service,
                  as: 'service',
                  include: [{ model: ServiceType, as: 'serviceType' }],
                },
              ],
            },
          ],
          where: filters.customerId
            ? { customerId: filters.customerId }
            : undefined,
        },
      ],
    });

    // 过滤照片条件
    const filteredRows = servicePhotos.filter(photo => {
      // 必须同时有服务前照片和服务后照片
      const hasBeforePhoto =
        photo.beforePhotos && photo.beforePhotos.length > 0;
      const hasAfterPhoto = photo.afterPhotos && photo.afterPhotos.length > 0;

      if (!hasBeforePhoto || !hasAfterPhoto) {
        return false;
      }

      // 检查是否有服务前照片（如果指定了筛选条件）
      if (filters.hasBeforePhoto !== undefined) {
        if (filters.hasBeforePhoto !== hasBeforePhoto) return false;
      }

      // 检查是否有服务后照片（如果指定了筛选条件）
      if (filters.hasAfterPhoto !== undefined) {
        if (filters.hasAfterPhoto !== hasAfterPhoto) return false;
      }

      // 检查服务类型
      if (filters.serviceTypeName) {
        const serviceTypeName =
          photo.order?.orderDetails?.[0]?.service?.serviceType?.name;
        if (
          !serviceTypeName ||
          !serviceTypeName.includes(filters.serviceTypeName)
        ) {
          return false;
        }
      }

      return true;
    });

    // 按订单服务时间倒序排序（最新的订单在上面）
    const sortedRows = filteredRows.sort((a, b) => {
      const timeA = a.order?.serviceTime
        ? new Date(a.order.serviceTime).getTime()
        : 0;
      const timeB = b.order?.serviceTime
        ? new Date(b.order.serviceTime).getTime()
        : 0;
      return timeB - timeA; // 倒序
    });

    // 分页处理
    const offset = (current - 1) * pageSize;
    const paginatedRows = sortedRows.slice(offset, offset + pageSize);

    return {
      list: paginatedRows.map(photo => ({
        id: photo.id,
        orderId: photo.orderId,
        employeeId: photo.employeeId,
        beforePhotos: photo.beforePhotos || [],
        afterPhotos: photo.afterPhotos || [],
        beforePhotoTime: photo.beforePhotoTime,
        afterPhotoTime: photo.afterPhotoTime,
        order: {
          id: photo.order?.id,
          sn: photo.order?.sn,
          status: photo.order?.status,
          serviceTime: photo.order?.serviceTime,
          customer: {
            id: photo.order?.customer?.id,
            nickname: photo.order?.customer?.nickname,
            phone: photo.order?.customer?.phone,
          },
          orderDetails: photo.order?.orderDetails?.map(detail => ({
            id: detail.id,
            serviceName: detail.serviceName,
            petName: detail.petName,
            petType: detail.petType,
            service: {
              id: detail.service?.id,
              serviceName: detail.service?.serviceName,
              serviceType: {
                id: detail.service?.serviceType?.id,
                name: detail.service?.serviceType?.name,
              },
            },
          })),
        },
      })),
      total: sortedRows.length,
    };
  }

  /**
   * 从选择的服务照片创建照片墙记录
   */
  async createFromSelectedPhotos(data: {
    servicePhotoId: number;
    selectedBeforePhoto: string;
    selectedAfterPhoto: string;
    title?: string;
    description?: string;
    priority?: number;
  }) {
    // 获取服务照片记录
    const servicePhoto = await ServicePhoto.findByPk(data.servicePhotoId, {
      include: [
        {
          model: Order,
          as: 'order',
          include: [
            { model: Customer, as: 'customer' },
            {
              model: OrderDetail,
              as: 'orderDetails',
              include: [
                { model: Pet, as: 'pet' },
                {
                  model: Service,
                  as: 'service',
                  include: [{ model: ServiceType, as: 'serviceType' }],
                },
              ],
            },
          ],
        },
      ],
    });

    if (!servicePhoto) {
      throw new CustomError('服务照片记录不存在');
    }

    // 验证选择的照片是否存在于服务照片中
    const beforePhotos = servicePhoto.beforePhotos || [];
    const afterPhotos = servicePhoto.afterPhotos || [];

    if (!beforePhotos.includes(data.selectedBeforePhoto)) {
      throw new CustomError('选择的服务前照片不存在');
    }

    if (!afterPhotos.includes(data.selectedAfterPhoto)) {
      throw new CustomError('选择的服务后照片不存在');
    }

    // 检查是否已存在该订单的照片墙记录
    const existing = await this.findByOrder(servicePhoto.orderId);
    if (existing) {
      throw new CustomError('该订单已存在照片墙记录');
    }

    const order = servicePhoto.order;

    // 构建照片墙数据
    const photoWallData = {
      orderId: servicePhoto.orderId,
      employeeId: servicePhoto.employeeId,
      customerId: order.customerId,
      beforePhoto: data.selectedBeforePhoto,
      afterPhoto: data.selectedAfterPhoto,
      title:
        data.title ||
        `${order.orderDetails?.[0]?.petName || '宠物'}的${
          order.orderDetails?.[0]?.service?.serviceType?.name || '服务'
        }`,
      description: data.description || '',
      serviceTypeName:
        order.orderDetails?.[0]?.service?.serviceType?.name || '',
      petName: order.orderDetails?.[0]?.petName || '',
      petType: order.orderDetails?.[0]?.petType || '',
      priority: data.priority || 0,
      isEnabled: true,
      likeCount: 0,
      viewCount: 0,
    };

    return await this.create(photoWallData);
  }
}
