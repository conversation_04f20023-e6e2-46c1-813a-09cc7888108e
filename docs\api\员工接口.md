# 员工相关API接口

## 员工状态枚举
```typescript
enum EmployeeStatus {
  在职 = '在职',
  离职 = '离职'
}

enum EmployeePosition {
  XI_HU_SHI = '洗护师',
  MEI_RONG_SHI = '美容师'
}
```

## 1. 员工管理接口

### 1.1 查询员工列表
- **接口**: `GET /employees`
- **描述**: 管理端查询员工列表，支持分页和筛选
- **参数**: 
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
  - `name` (string): 员工姓名筛选
  - `phone` (string): 手机号筛选
  - `status` (string): 状态筛选
  - `position` (string): 职位筛选
  - `includeOrders` (string): 是否包含订单信息

### 1.2 查询员工详情
- **接口**: `GET /employees/{id}`
- **描述**: 查询指定员工的详细信息
- **参数**: `id` (number): 员工ID

### 1.3 创建员工
- **接口**: `POST /employees`
- **描述**: 创建新员工
- **请求体**: 
  ```json
  {
    "name": "张三",
    "phone": "13800138000",
    "position": "XI_HU_SHI",
    "level": 3,
    "workExp": 2,
    "idCard": "110101199001011234",
    "hireDate": "2024-01-01",
    "certificates": ["宠物美容师证", "动物护理证"]
  }
  ```

### 1.4 更新员工
- **接口**: `PUT /employees/{id}`
- **描述**: 更新员工信息
- **参数**: `id` (number): 员工ID
- **请求体**: 包含需要更新的字段

### 1.5 删除员工
- **接口**: `DELETE /employees/{id}`
- **描述**: 删除员工（软删除）
- **参数**: `id` (number): 员工ID

### 1.6 更新员工钱包余额
- **接口**: `PUT /employees/{id}/wallet`
- **描述**: 更新员工钱包余额
- **参数**: 
  - `id` (number): 员工ID
  - `amount` (number): 金额

### 1.7 获取可选择员工列表
- **接口**: `GET /openapi/employees`
- **描述**: 获取可选择的员工列表（无需认证）
- **参数**: 
  - `serviceTypeId` (number): 服务类型ID筛选
  - `current` (number): 页码
  - `pageSize` (number): 每页数量

### 1.8 获取客户可选员工
- **接口**: `GET /customers/{customerId}/employees`
- **描述**: 获取指定客户可选择的员工列表
- **参数**: 
  - `customerId` (number): 客户ID
  - `serviceTypeId` (number): 服务类型ID
  - `current` (number): 页码
  - `pageSize` (number): 每页数量

## 2. 员工个人信息接口

### 2.1 员工修改个人信息
- **接口**: `PUT /employee/profile`
- **描述**: 员工端修改个人信息
- **请求体**: 
  ```json
  {
    "name": "新姓名",
    "avatar": "头像URL"
  }
  ```

### 2.2 获取员工个人信息
- **接口**: `GET /employee/profile`
- **描述**: 员工端获取个人信息
- **参数**: 无（从token获取员工ID）

## 3. 员工出车拍照接口

### 3.1 员工出车拍照打卡
- **接口**: `POST /openapi/employee-checkins`
- **描述**: 员工出车拍照打卡
- **请求体**: 
  ```json
  {
    "employeeId": 1,
    "vehiclePhotos": ["url1", "url2"],
    "staffPhotos": ["url3", "url4"],
    "interiorPhotos": ["url5", "url6"],
    "description": "出车打卡",
    "latitude": 39.916527,
    "longitude": 116.397128,
    "address": "北京市朝阳区某某街道"
  }
  ```

### 3.2 查询员工打卡记录
- **接口**: `GET /openapi/employee-checkins/employee/{employeeId}`
- **描述**: 查询指定员工的打卡记录列表
- **参数**: 
  - `employeeId` (number): 员工ID
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

### 3.3 查询今日打卡记录
- **接口**: `GET /openapi/employee-checkins/employee/{employeeId}/today`
- **描述**: 查询员工今日打卡记录
- **参数**: `employeeId` (number): 员工ID

### 3.4 查询打卡统计
- **接口**: `GET /openapi/employee-checkins/employee/{employeeId}/stats`
- **描述**: 查询员工打卡统计信息
- **参数**: 
  - `employeeId` (number): 员工ID
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

### 3.5 管理端查询所有打卡记录
- **接口**: `GET /employee-checkins`
- **描述**: 管理端查询所有员工打卡记录
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量
  - `employeeId` (number): 员工ID筛选
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期
  - `keyword` (string): 关键词搜索

### 3.6 删除打卡记录
- **接口**: `DELETE /employee-checkins/{id}`
- **描述**: 删除指定打卡记录
- **参数**: `id` (number): 打卡记录ID

### 3.7 批量删除打卡记录
- **接口**: `DELETE /employee-checkins/batch`
- **描述**: 批量删除打卡记录
- **请求体**: 
  ```json
  {
    "ids": [1, 2, 3]
  }
  ```

## 4. 员工考勤接口

### 4.1 查询考勤列表
- **接口**: `GET /attendances`
- **描述**: 查询考勤记录列表
- **参数**: 
  - `current` (number): 页码
  - `pageSize` (number): 每页数量

### 4.2 新增考勤
- **接口**: `POST /attendances`
- **描述**: 新增考勤记录
- **请求体**: 包含考勤信息

### 4.3 删除考勤
- **接口**: `DELETE /attendances/{id}`
- **描述**: 删除考勤记录
- **参数**: `id` (number): 考勤记录ID

### 4.4 查询员工考勤记录
- **接口**: `GET /attendances/employee/{employeeId}`
- **描述**: 查询指定员工的考勤记录
- **参数**: `employeeId` (number): 员工ID

### 4.5 按日期查询考勤记录
- **接口**: `GET /attendances/date/{startDate}/{endDate}`
- **描述**: 按日期范围查询考勤记录
- **参数**: 
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期

## 5. 员工位置服务接口

### 5.1 员工上报位置
- **接口**: `POST /openapi/location/updateEmployeeLocation`
- **描述**: 员工端定时上报当前位置
- **请求体**: 
  ```json
  {
    "employeeId": 1,
    "latitude": 39.916527,
    "longitude": 116.397128,
    "address": "北京市朝阳区某某街道"
  }
  ```

### 5.2 查询员工位置
- **接口**: `GET /location/employee/{employeeId}`
- **描述**: 查询员工当前位置
- **参数**: `employeeId` (number): 员工ID

## 6. 员工动作记录接口

### 6.1 获取员工动作记录列表
- **接口**: `GET /employee-action-logs/list`
- **描述**: 获取员工动作记录列表
- **参数**: 
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期
  - `employeeId` (number): 员工ID
  - `orderId` (number): 订单ID
  - `changeType` (string): 动作类型
  - `page` (number): 页码
  - `pageSize` (number): 每页数量

### 6.2 获取员工动作统计
- **接口**: `GET /employee-action-logs/stats`
- **描述**: 获取员工动作统计信息
- **参数**: 
  - `startDate` (string): 开始日期
  - `endDate` (string): 结束日期
  - `employeeId` (number): 员工ID

## 7. 服务照片接口

### 7.1 创建服务照片记录
- **接口**: `POST /service-photos`
- **描述**: 创建服务照片记录
- **请求体**: 包含照片信息

### 7.2 删除服务照片
- **接口**: `DELETE /service-photos/{id}`
- **描述**: 删除服务照片
- **参数**: `id` (number): 照片记录ID

### 7.3 查询订单服务照片
- **接口**: `GET /service-photos/order/{orderId}`
- **描述**: 查询指定订单的服务照片
- **参数**: `orderId` (number): 订单ID

### 7.4 查询员工服务照片列表
- **接口**: `GET /service-photos/employee/{employeeId}`
- **描述**: 查询指定员工的服务照片列表
- **参数**: 
  - `employeeId` (number): 员工ID
  - `current` (number): 页码
  - `pageSize` (number): 每页数量

### 7.5 设置服务前照片
- **接口**: `POST /service-photos/set-before-photos`
- **描述**: 设置服务前照片（完整替换）
- **请求体**: 包含照片URL数组

## 8. 追加服务管理接口

### 8.1 查询待确认的追加服务
- **接口**: `GET /employee/additional-services/pending`
- **描述**: 员工查询待确认的追加服务申请
- **参数**:
  - `employeeId` (number): 员工ID
  - `current` (number): 页码，默认1
  - `pageSize` (number): 每页数量，默认10
- **返回数据结构**:
```json
{
  "list": [
    {
      "id": 41,
      "sn": "ADD17252410888292746",
      "status": "pending_confirm",
      "totalFee": 15.00,
      "details": [
        {
          "id": 45,
          "serviceId": 1,
          "serviceName": "局部去油",
          "servicePrice": 15.00,
          "quantity": 1,
          "additionalService": {
            "id": 1,
            "name": "局部去油",
            "needDurationTracking": true
          }
        }
      ],
      "orderDetail": {
        "order": {
          "customer": {
            "id": 123,
            "nickname": "张三",
            "phone": "13800138000"
          }
        }
      },
      "customer": {
        "id": 123,
        "nickname": "张三",
        "phone": "13800138000"
      }
    }
  ],
  "total": 5,
  "current": 1,
  "pageSize": 10
}
```

**字段说明**:
- 已修复实体关联问题，现在可以正确获取 `additionalService` 信息
- `needDurationTracking`: 是否需要计时（从关联的增项服务获取）
- 数据结构优化，提供更清晰的服务信息

## 业务规则说明

### 职位权限规则
- **洗护师**: 只能接洗护类订单
- **美容师**: 可以接洗护和美容类订单

### 出车拍照规则
- 支持三个分组：车辆外观、服务人员、车内情况
- 每个分组最多9张照片
- 至少需要上传一个分组的照片

### 权限控制
- 员工只能查看和修改自己的信息
- 管理员可以操作所有员工信息
- 员工个人信息修改仅限昵称和头像
