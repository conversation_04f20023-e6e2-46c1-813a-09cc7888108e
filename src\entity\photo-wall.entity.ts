import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Order } from './order.entity';

export interface PhotoWallAttributes {
  /** 照片ID */
  id: number;
  /** 关联订单ID */
  orderId?: number;
  /** 关联员工ID */
  employeeId?: number;
  /** 关联客户ID */
  customerId?: number;
  /** 服务前照片链接 */
  beforePhoto: string;
  /** 服务后照片链接 */
  afterPhoto: string;
  /** 照片标题 */
  title?: string;
  /** 照片描述 */
  description?: string;
  /** 服务类型名称 */
  serviceTypeName?: string;
  /** 宠物名称 */
  petName?: string;
  /** 宠物类型 */
  petType?: string;
  /** 是否启用展示 */
  isEnabled: boolean;
  /** 展示优先级 */
  priority: number;
  /** 点赞数 */
  likeCount: number;
  /** 浏览数 */
  viewCount: number;
  /** 关联的订单信息 */
  order?: Order;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({ tableName: 'photo_walls', timestamps: true, comment: '照片墙表' })
export class PhotoWall
  extends Model<PhotoWallAttributes>
  implements PhotoWallAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '照片ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    comment: '关联订单ID',
  })
  @ForeignKey(() => Order)
  orderId: number;

  @Column({
    type: DataType.INTEGER,
    comment: '关联员工ID',
  })
  employeeId: number;

  @Column({
    type: DataType.INTEGER,
    comment: '关联客户ID',
  })
  customerId: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '服务前照片链接',
  })
  beforePhoto: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '服务后照片链接',
  })
  afterPhoto: string;

  @Column({
    type: DataType.STRING(100),
    comment: '照片标题',
  })
  title: string;

  @Column({
    type: DataType.STRING(500),
    comment: '照片描述',
  })
  description: string;

  @Column({
    type: DataType.STRING(50),
    comment: '服务类型名称',
  })
  serviceTypeName: string;

  @Column({
    type: DataType.STRING(50),
    comment: '宠物名称',
  })
  petName: string;

  @Column({
    type: DataType.STRING(50),
    comment: '宠物类型',
  })
  petType: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
    comment: '是否启用展示',
  })
  isEnabled: boolean;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '展示优先级',
  })
  priority: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '点赞数',
  })
  likeCount: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '浏览数',
  })
  viewCount: number;

  @BelongsTo(() => Order, {
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  order: Order;
}
