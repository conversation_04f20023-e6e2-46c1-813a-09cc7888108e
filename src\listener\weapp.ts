import { HttpService, HttpServiceFactory } from '@midwayjs/axios';
import {
  Config,
  ILogger,
  InjectClient,
  Logger,
  Provide,
  Scope,
  ScopeEnum,
} from '@midwayjs/core';
import { DataListener } from '@midwayjs/core';

@Provide()
@Scope(ScopeEnum.Singleton)
export class WeappListener extends DataListener<{
  token_weapp: string;
  token_weapp_employee: string;
}> {
  @InjectClient(HttpServiceFactory, 'weapp')
  weapp: HttpService;

  @Config('weapp')
  weappConfig: {
    appid: string;
    secret: string;
    sym_sn: string;
    sym_key: string;
  };

  @Config('weapp_employee')
  weappEmployeeConfig: {
    appid: string;
    secret: string;
    sym_sn: string;
    sym_key: string;
  };

  @Logger('coreLogger')
  logger: ILogger;

  private intervalHandler;

  // 初始化数据
  async initData() {
    return await this.getAccessToken();
  }

  // 更新数据
  onData(setData: (arg0: any) => void) {
    this.intervalHandler = setInterval(async () => {
      const newData = await this.getAccessToken();
      if (newData) {
        setData(newData);
      }
      // access_token的有效其为2小时，所以设置为40分钟刷新一次，这样可以容错两次
    }, 1000 * 60 * 40);
  }

  // 清理资源
  async destroyListener() {
    // 关闭定时器
    clearInterval(this.intervalHandler);
    // 其他清理, close sdk 等等
  }

  /**
   * 获取小程序 access_token
   */
  async getAccessToken() {
    this.logger.info('获取小程序 access_token');
    console.log('获取小程序 access_token -- token_weapp');
    let token_weapp: string | null;
    const res = await this.weapp.post('/cgi-bin/stable_token', {
      grant_type: 'client_credential',
      appid: this.weappConfig.appid,
      secret: this.weappConfig.secret,
    });

    if (res.status !== 200) {
      console.log(res.data);
      this.logger.error(res.data);
      token_weapp = null;
    } else {
      const { access_token } = res.data;
      if (!access_token) {
        this.logger.error(res.data);
        console.log('微信接口【token_weapp】返回错误： %j', res.data);
        token_weapp = null;
      } else {
        token_weapp = access_token;
      }
    }

    console.log('获取小程序 access_token -- token_weapp_employee');
    let token_weapp_employee: string | null;
    const res1 = await this.weapp.post('/cgi-bin/stable_token', {
      grant_type: 'client_credential',
      appid: this.weappEmployeeConfig.appid,
      secret: this.weappEmployeeConfig.secret,
    });

    if (res1.status !== 200) {
      console.log(res1.data);
      this.logger.error(res1.data);
      token_weapp_employee = null;
    } else {
      const { access_token } = res1.data;
      if (!access_token) {
        this.logger.error(res1.data);
        console.log('微信接口【token_weapp_employee】返回错误： %j', res1.data);
        token_weapp_employee = null;
      } else {
        token_weapp_employee = access_token;
      }
    }
    console.log('获取小程序 access_token 完成');
    return {
      token_weapp,
      token_weapp_employee,
    };
  }
}
