import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  BelongsTo,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { Service } from './service.entity';

export interface ServicePriceHistoryAttributes {
  /** 历史记录ID */
  id: number;
  /** 关联服务ID */
  serviceId: number;
  /** 变更前价格 */
  oldPrice: number;
  /** 变更后价格 */
  newPrice: number;
  /** 变更原因 */
  changeReason: string;
  /** 变更操作人 */
  changedBy: string;
  /** 变更时间 */
  changeTime: Date;
  /** 生效时间 */
  effectiveTime: Date;
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 关联服务信息 */
  service?: Service;
}

@Table({
  tableName: 'service_price_histories',
  comment: '服务价格历史记录表',
})
export class ServicePriceHistory extends Model<ServicePriceHistoryAttributes> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '历史记录ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联服务ID',
  })
  @ForeignKey(() => Service)
  serviceId: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '变更前价格',
  })
  oldPrice: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '变更后价格',
  })
  newPrice: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '变更原因',
  })
  changeReason: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '变更操作人',
  })
  changedBy: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '变更时间',
  })
  changeTime: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '生效时间',
  })
  effectiveTime: Date;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '备注',
  })
  remark?: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    comment: '创建时间',
  })
  createdAt: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    comment: '更新时间',
  })
  updatedAt: Date;

  @BelongsTo(() => Service)
  service: Service;
}
