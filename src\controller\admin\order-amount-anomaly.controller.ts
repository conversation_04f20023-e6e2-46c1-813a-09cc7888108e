import { Controller, Get, Post, Inject, Query, Body } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { OrderAmountAnomalyService } from '../../service/order-amount-anomaly.service';
import { CustomError } from '../../error/custom.error';

@Controller('/admin/order-amount-anomalies')
export class AdminOrderAmountAnomalyController {
  @Inject()
  ctx: Context;

  @Inject()
  orderAmountAnomalyService: OrderAmountAnomalyService;

  @Get('/check', { summary: '检查订单金额异常并生成修复建议' })
  async checkAnomaliesWithSuggestions(
    @Query('clearExistingRecords') clearExistingRecords?: string,
    @Query('orderId') orderId?: number
  ) {
    return await this.orderAmountAnomalyService.generateFixSuggestions({
      clearExistingRecords: clearExistingRecords === 'true',
      orderId: orderId ? parseInt(orderId.toString()) : undefined,
    });
  }

  @Post('/apply-fix', { summary: '应用选定的修复方案' })
  async applyFixSuggestion(
    @Body()
    body: {
      orderId: number;
      suggestionId: string; // 方案ID，如：'proportional_adjust', 'keep_card_priority' 等
      operatorId: number;
      operatorName: string;
      remark?: string;
      confirmRisk?: boolean; // 对于高风险操作需要确认
    }
  ) {
    const {
      orderId,
      suggestionId,
      operatorId,
      operatorName,
      confirmRisk = false,
    } = body;

    if (!orderId || !suggestionId || !operatorId || !operatorName) {
      throw new CustomError('参数不完整');
    }

    // 对于修改实付金额的高风险操作，需要确认
    if (suggestionId === 'fix_total_fee' && !confirmRisk) {
      return {
        error: '高风险操作需要确认',
        message:
          '修改实付金额与银行流水相关，请设置 confirmRisk: true 确认操作',
        warning: '请确保银行实际扣款金额与修改后的金额一致',
      };
    }

    return await this.orderAmountAnomalyService.applyFixBySuggestionId(body);
  }
}
