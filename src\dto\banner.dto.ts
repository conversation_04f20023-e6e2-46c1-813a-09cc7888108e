import { Rule, RuleType } from '@midwayjs/validate';
import { JumpType } from '../entity/banner.entity';

export class CreateBannerDto {
  @Rule(
    RuleType.string().required().messages({
      'string.empty': '图片链接不能为空',
      'any.required': '图片链接是必填项',
    })
  )
  imageURL: string;

  @Rule(
    RuleType.string()
      .valid(...Object.values(JumpType))
      .allow(null)
      .messages({
        'any.only': '跳转类型只能是custom（自定义链接）或activity（活动页）',
      })
  )
  jumpType?: JumpType;

  @Rule(
    RuleType.string().allow(null, '').messages({
      'string.base': '跳转链接必须是字符串',
    })
  )
  jumpLink?: string;

  @Rule(
    RuleType.number().integer().min(1).max(10).default(5).messages({
      'number.base': '优先级必须是数字',
      'number.integer': '优先级必须是整数',
      'number.min': '优先级最小值为1',
      'number.max': '优先级最大值为10',
    })
  )
  priority: number;
}

export class UpdateBannerDto {
  @Rule(
    RuleType.string().messages({
      'string.empty': '图片链接不能为空',
    })
  )
  imageURL?: string;

  @Rule(
    RuleType.string()
      .valid(...Object.values(JumpType))
      .allow(null)
      .messages({
        'any.only': '跳转类型只能是custom（自定义链接）或activity（活动页）',
      })
  )
  jumpType?: JumpType;

  @Rule(
    RuleType.string().allow(null, '').messages({
      'string.base': '跳转链接必须是字符串',
    })
  )
  jumpLink?: string;

  @Rule(
    RuleType.number().integer().min(1).max(10).messages({
      'number.base': '优先级必须是数字',
      'number.integer': '优先级必须是整数',
      'number.min': '优先级最小值为1',
      'number.max': '优先级最大值为10',
    })
  )
  priority?: number;
}

export class QueryBannerDto {
  @Rule(
    RuleType.number().integer().min(0).messages({
      'number.base': 'offset必须是数字',
      'number.integer': 'offset必须是整数',
      'number.min': 'offset最小值为0',
    })
  )
  offset?: number;

  @Rule(
    RuleType.number().integer().positive().messages({
      'number.base': 'limit必须是数字',
      'number.integer': 'limit必须是整数',
      'number.positive': 'limit必须是正数',
    })
  )
  limit?: number;

  @Rule(
    RuleType.number().integer().positive().default(1).messages({
      'number.base': '当前页码必须是数字',
      'number.integer': '当前页码必须是整数',
      'number.positive': '当前页码必须是正数',
    })
  )
  current?: number;

  @Rule(
    RuleType.number().integer().positive().default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.positive': '每页数量必须是正数',
    })
  )
  pageSize?: number;

  @Rule(
    RuleType.string().messages({
      'string.base': '图片链接必须是字符串',
    })
  )
  imageURL?: string;

  @Rule(
    RuleType.string()
      .valid(...Object.values(JumpType))
      .allow(null)
      .messages({
        'any.only': '跳转类型只能是custom（自定义链接）或activity（活动页）',
      })
  )
  jumpType?: JumpType;

  @Rule(
    RuleType.number().integer().min(1).max(10).messages({
      'number.base': '优先级必须是数字',
      'number.integer': '优先级必须是整数',
      'number.min': '优先级最小值为1',
      'number.max': '优先级最大值为10',
    })
  )
  priority?: number;
}

export class UpdateBannerPriorityDto {
  @Rule(
    RuleType.number().integer().min(1).max(10).required().messages({
      'number.base': '优先级必须是数字',
      'number.integer': '优先级必须是整数',
      'number.min': '优先级最小值为1',
      'number.max': '优先级最大值为10',
      'any.required': '优先级是必填项',
    })
  )
  priority: number;
}
