import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface MessageTemplateAttributes {
  /** 模板ID */
  id: number;
  /** 模板代码 */
  code: string;
  /** 消息类型 */
  type: 'system' | 'platform' | 'order';
  /** 消息标题模板 */
  title: string;
  /** 消息内容模板 */
  content: string;
  /** 模板变量说明 */
  variables?: string;
  /** 是否启用 */
  isActive: boolean;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'message_templates',
  timestamps: true,
  comment: '消息模板表',
})
export class MessageTemplate
  extends Model<MessageTemplateAttributes>
  implements MessageTemplateAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '模板ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    unique: {
      name: 'uk_code',
      msg: '模板代码已存在',
    },
    comment: '模板代码',
  })
  code: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '消息类型',
  })
  type: 'system' | 'platform' | 'order';

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '消息标题模板',
  })
  title: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '消息内容模板',
  })
  content: string;

  @Column({
    type: DataType.TEXT,
    comment: '模板变量说明',
  })
  variables?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否启用',
  })
  isActive: boolean;
}
