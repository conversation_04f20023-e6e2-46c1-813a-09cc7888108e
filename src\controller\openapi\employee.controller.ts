import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { EmployeeService } from '../../service/employee.service';
import { Vehicle } from '../../entity';

@Controller('/openapi/employees')
export class OpenApiEmployeeController {
  @Inject()
  service: EmployeeService;

  @Get('/', { summary: '获取可选择的员工列表（无需认证）' })
  async getAvailableEmployees(
    @Query('serviceTypeId') serviceTypeId?: number,
    @Query('current') current = 1,
    @Query('pageSize') pageSize = 10
  ) {
    // 确保分页参数是数字类型
    const currentPage = Number(current) || 1;
    const pageSizeNum = Number(pageSize) || 10;

    const offset = (currentPage - 1) * pageSizeNum;
    const limit = pageSizeNum;

    // 构建查询条件
    const whereCondition: any = {
      status: 1, // 只查询在职的员工
    };

    // 构建include条件
    const includeConditions: any[] = [
      {
        model: Vehicle,
        required: false, // 左连接，允许员工没有车辆
      },
    ];

    const result = await this.service.findAll({
      query: whereCondition,
      offset,
      limit,
      include: includeConditions,
      order: [
        ['rating', 'DESC'], // 按评分降序
        ['workExp', 'DESC'], // 按工作经验降序
        ['level', 'DESC'], // 按等级降序
      ],
    });

    // 只返回必要的字段
    const filteredList = result.list.map(employee => ({
      id: employee.id,
      name: employee.name,
      avatar: employee.avatar,
      level: employee.level,
      workExp: employee.workExp,
      rating: employee.rating,
      phone: employee.phone,
      vehicle: employee.vehicle
        ? {
            id: employee.vehicle.id,
            plateNumber: employee.vehicle.plateNumber,
            status: employee.vehicle.status,
          }
        : null,
    }));

    return {
      list: filteredList,
      total: result.total,
      current: currentPage,
      pageSize: pageSizeNum,
    };
  }
}
