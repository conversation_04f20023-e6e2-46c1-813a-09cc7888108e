import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComplaintService } from '../service/complaint.service';
import { Op } from 'sequelize';
import { Customer, Order, Employee } from '../entity';
import {
  CreateComplaintDto,
  UpdateComplaintDto,
  QueryComplaintDto,
  HandleComplaintDto,
  ComplaintStatisticsDto,
} from '../dto/complaint.dto';
import { CustomError } from '../error/custom.error';

@Controller('/complaints')
export class ComplaintController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComplaintService;

  @Get('/', { summary: '查询投诉建议列表（管理端）' })
  async index(@Query() query: QueryComplaintDto) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      keyword,
      startDate,
      endDate,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // 构建查询条件
    const whereCondition: any = { ...queryInfo };

    // 管理端查询时，只查询用户端的投诉建议（customerId不为null）
    whereCondition.customerId = { [Op.ne]: null };

    // 处理日期范围查询
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 处理关键词搜索（标题和内容）
    if (keyword) {
      whereCondition[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } },
      ];
    }

    return this.service.findAll({
      query: whereCondition,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
        },
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
        },
      ],
    });
  }

  @Get('/statistics', { summary: '获取投诉建议统计信息' })
  async getStatistics(@Query() query: ComplaintStatisticsDto) {
    return await this.service.getComplaintStatistics(query);
  }

  @Get('/:id', { summary: '按ID查询投诉建议' })
  async show(@Param('id') id: number) {
    const res = await this.service.getComplaintWithRelations(id);
    if (!res) {
      throw new CustomError('未找到指定投诉建议');
    }
    return res;
  }

  @Post('/', { summary: '创建投诉建议（用户端）' })
  async create(@Body() createDto: CreateComplaintDto & { customerId: number }) {
    const { customerId, ...complaintData } = createDto;

    if (!customerId) {
      throw new CustomError('客户ID不能为空', 400);
    }

    // 验证必填字段的业务逻辑
    if (complaintData.subCategory === 'order' && !complaintData.orderId) {
      throw new CustomError('订单投诉必须提供订单ID', 400);
    }

    if (complaintData.subCategory === 'employee' && !complaintData.employeeId) {
      throw new CustomError('人员投诉必须提供员工ID', 400);
    }

    return await this.service.createComplaint(customerId, complaintData);
  }

  @Put('/:id', { summary: '更新投诉建议（用户端）' })
  async update(
    @Param('id') id: number,
    @Body() updateDto: UpdateComplaintDto & { customerId: number }
  ) {
    const { customerId, ...updateData } = updateDto;
    if (!customerId) {
      throw new CustomError('客户ID不能为空', 400);
    }

    // 验证必填字段的业务逻辑
    if (updateData.subCategory === 'order' && !updateData.orderId) {
      throw new CustomError('订单投诉必须提供订单ID', 400);
    }

    if (updateData.subCategory === 'employee' && !updateData.employeeId) {
      throw new CustomError('人员投诉必须提供员工ID', 400);
    }

    return await this.service.updateComplaint(customerId, id, updateData);
  }

  @Del('/:id', { summary: '删除投诉建议' })
  async delete(@Param('id') id: number) {
    return await this.service.deleteComplaint(id);
  }

  @Put('/:id/handle', { summary: '处理投诉建议' })
  async handle(@Param('id') id: number, @Body() handleDto: HandleComplaintDto) {
    // 从登录信息中获取处理人员ID
    const currentUser = this.ctx.state.user;
    if (!currentUser || !currentUser.userId) {
      throw new CustomError('请先登录', 401);
    }

    const handlerId = currentUser.userId;
    const handleData = {
      ...handleDto,
      handlerId,
    };

    return await this.service.handleComplaint(id, handleData);
  }

  @Get('/customer/:customerId', { summary: '获取客户投诉建议列表' })
  async getCustomerComplaints(
    @Param('customerId') customerId: number,
    @Query('page') page = 1,
    @Query('pageSize') pageSize = 10
  ) {
    return await this.service.getCustomerComplaints(customerId, page, pageSize);
  }

  @Get('/order/:orderId', { summary: '根据订单ID获取投诉建议' })
  async getByOrderId(@Param('orderId') orderId: number) {
    const complaints = await this.service.findAll({
      query: { orderId },
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
        },
        {
          model: Order,
          attributes: ['id', 'sn', 'status', 'serviceTime'],
        },
      ],
    });

    return complaints;
  }

  @Get('/employee/:employeeId', { summary: '根据员工ID获取投诉建议' })
  async getByEmployeeId(@Param('employeeId') employeeId: number) {
    const complaints = await this.service.findAll({
      query: { employeeId, customerId: { [Op.ne]: null } }, // 只查询用户端的投诉
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
        },
      ],
    });

    return complaints;
  }

  @Get('/employee-suggestions', { summary: '查询员工建议列表（管理端）' })
  async getEmployeeSuggestions(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      keyword,
      startDate,
      endDate,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // 构建查询条件 - 只查询员工建议
    const whereCondition: any = {
      ...queryInfo,
      category: 'suggestion',
      customerId: null, // 员工建议的customerId为null
    };

    // 处理日期范围查询
    if (startDate || endDate) {
      whereCondition.createdAt = {};
      if (startDate) {
        whereCondition.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereCondition.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    // 处理关键词搜索（标题和内容）
    if (keyword) {
      whereCondition[Op.or] = [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } },
      ];
    }

    return this.service.findAll({
      query: whereCondition,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
        },
      ],
    });
  }
}
