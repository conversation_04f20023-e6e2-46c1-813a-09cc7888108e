import { Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { ComplaintOperationLog } from '../entity/complaint-operation-log.entity';
import { Customer } from '../entity/customer.entity';
import { Employee } from '../entity/employee.entity';

@Provide()
export class ComplaintOperationLogService extends BaseService<ComplaintOperationLog> {
  getModel() {
    return ComplaintOperationLog;
  }

  /**
   * 记录投诉建议操作日志
   * @param logData 日志数据
   */
  async createOperationLog(logData: {
    complaintId: number;
    operationType:
      | 'create'
      | 'update'
      | 'status_change'
      | 'handle'
      | 'reply'
      | 'delete';
    operatorType: 'customer' | 'employee' | 'admin' | 'system';
    operatorId?: number;
    operatorName?: string;
    beforeStatus?: string;
    afterStatus?: string;
    description: string;
    operationDetails?: any;
  }) {
    const {
      complaintId,
      operationType,
      operatorType,
      operatorId,
      operatorName,
      beforeStatus,
      afterStatus,
      description,
      operationDetails,
    } = logData;

    // 如果没有提供操作人姓名，尝试从数据库获取
    let finalOperatorName = operatorName;
    if (!finalOperatorName && operatorId) {
      if (operatorType === 'customer') {
        const customer = await Customer.findByPk(operatorId, {
          attributes: ['nickname'],
        });
        finalOperatorName = customer?.nickname || `客户${operatorId}`;
      } else if (operatorType === 'employee') {
        const employee = await Employee.findByPk(operatorId, {
          attributes: ['name'],
        });
        finalOperatorName = employee?.name || `员工${operatorId}`;
      } else if (operatorType === 'admin') {
        finalOperatorName = `管理员${operatorId}`;
      }
    }

    return await ComplaintOperationLog.create({
      complaintId,
      operationType,
      operatorType,
      operatorId,
      operatorName: finalOperatorName,
      beforeStatus,
      afterStatus,
      description,
      operationDetails: operationDetails
        ? JSON.stringify(operationDetails)
        : undefined,
      operatedAt: new Date(),
    });
  }

  /**
   * 获取投诉建议的操作历史记录
   * @param complaintId 投诉建议ID
   * @param page 页码
   * @param pageSize 每页数量
   */
  async getComplaintOperationHistory(
    complaintId: number,
    page = 1,
    pageSize = 20
  ) {
    const offset = (page - 1) * pageSize;

    const result = await this.findAll({
      query: { complaintId },
      offset,
      limit: pageSize,
      order: [['operatedAt', 'DESC']],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar'],
          required: false,
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar'],
          required: false,
        },
      ],
    });

    // 格式化返回数据
    const formattedData = result.list.map((log: any) => {
      const logData = log.toJSON();

      // 解析操作详情
      if (logData.operationDetails) {
        try {
          logData.operationDetails = JSON.parse(logData.operationDetails);
        } catch (e) {
          // 如果解析失败，保持原始字符串
        }
      }

      // 格式化操作人信息
      let operatorInfo = null;
      if (logData.operatorType === 'customer' && logData.customer) {
        operatorInfo = {
          type: 'customer',
          id: logData.customer.id,
          name: logData.customer.nickname,
          phone: logData.customer.phone,
          avatar: logData.customer.avatar,
        };
      } else if (logData.operatorType === 'employee' && logData.employee) {
        operatorInfo = {
          type: 'employee',
          id: logData.employee.id,
          name: logData.employee.name,
          phone: logData.employee.phone,
          avatar: logData.employee.avatar,
        };
      } else if (logData.operatorType === 'admin') {
        operatorInfo = {
          type: 'admin',
          id: logData.operatorId,
          name: logData.operatorName || `管理员${logData.operatorId}`,
        };
      } else if (logData.operatorType === 'system') {
        operatorInfo = {
          type: 'system',
          name: '系统',
        };
      }

      return {
        id: logData.id,
        operationType: logData.operationType,
        operatorType: logData.operatorType,
        operator: operatorInfo,
        beforeStatus: logData.beforeStatus,
        afterStatus: logData.afterStatus,
        description: logData.description,
        operationDetails: logData.operationDetails,
        operatedAt: logData.operatedAt,
      };
    });

    return {
      data: formattedData,
      total: result.total,
      page,
      pageSize,
      totalPages: Math.ceil(result.total / pageSize),
    };
  }

  /**
   * 批量记录操作日志
   * @param logs 日志数组
   */
  async batchCreateOperationLogs(
    logs: Array<{
      complaintId: number;
      operationType:
        | 'create'
        | 'update'
        | 'status_change'
        | 'handle'
        | 'reply'
        | 'delete';
      operatorType: 'customer' | 'employee' | 'admin' | 'system';
      operatorId?: number;
      operatorName?: string;
      beforeStatus?: string;
      afterStatus?: string;
      description: string;
      operationDetails?: any;
    }>
  ) {
    const logRecords = logs.map(log => ({
      ...log,
      operationDetails: log.operationDetails
        ? JSON.stringify(log.operationDetails)
        : undefined,
      operatedAt: new Date(),
    }));

    return await ComplaintOperationLog.bulkCreate(logRecords);
  }
}
