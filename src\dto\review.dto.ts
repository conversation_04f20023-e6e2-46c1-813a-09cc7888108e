import { Rule, RuleType } from '@midwayjs/validate';
import { BaseQueryDTO } from './BaseDTO';

/**
 * 创建评价DTO
 */
export class CreateReviewDto {
  @Rule(RuleType.number().required().error(new Error('订单ID不能为空')))
  /** 订单ID */
  orderId: number;

  @Rule(
    RuleType.number()
      .required()
      .min(1)
      .max(5)
      .error(new Error('评分必须在1-5之间'))
  )
  /** 评分，1-5星 */
  rating: number;

  @Rule(
    RuleType.string()
      .required()
      .max(500)
      .error(new Error('评价内容不能为空且不能超过500字符'))
  )
  /** 评价内容 */
  comment: string;

  @Rule(
    RuleType.array()
      .items(RuleType.string())
      .max(6)
      .optional()
      .error(new Error('图片最多6张'))
  )
  /** 图片URL数组 */
  photoURLs?: string[];
}

/**
 * 更新评价DTO
 */
export class UpdateReviewDto {
  @Rule(
    RuleType.number()
      .optional()
      .min(1)
      .max(5)
      .error(new Error('评分必须在1-5之间'))
  )
  /** 评分，1-5星 */
  rating?: number;

  @Rule(
    RuleType.string()
      .optional()
      .max(500)
      .error(new Error('评价内容不能超过500字符'))
  )
  /** 评价内容 */
  comment?: string;

  @Rule(
    RuleType.array()
      .items(RuleType.string())
      .max(6)
      .optional()
      .error(new Error('图片最多6张'))
  )
  /** 图片URL数组 */
  photoURLs?: string[];
}

/**
 * 查询评价DTO
 */
export class QueryReviewDto extends BaseQueryDTO {
  @Rule(RuleType.number().optional().error(new Error('客户ID必须是数字')))
  customerId?: number;

  @Rule(RuleType.number().optional().error(new Error('订单ID必须是数字')))
  orderId?: number;

  @Rule(RuleType.number().optional().error(new Error('服务ID必须是数字')))
  serviceId?: number;

  @Rule(
    RuleType.number()
      .optional()
      .min(1)
      .max(5)
      .error(new Error('评分必须在1-5之间'))
  )
  rating?: number;

  @Rule(RuleType.string().optional().error(new Error('搜索关键词必须是字符串')))
  keyword?: string;

  @Rule(RuleType.string().optional().error(new Error('开始日期格式错误')))
  startDate?: string;

  @Rule(RuleType.string().optional().error(new Error('结束日期格式错误')))
  endDate?: string;

  @Rule(RuleType.number().optional().error(new Error('员工ID必须是数字')))
  employeeId?: number;
}

/**
 * 评价趋势查询DTO
 */
export class ReviewTrendDto {
  @Rule(RuleType.string().required().error(new Error('开始日期不能为空')))
  startDate: string;

  @Rule(RuleType.string().required().error(new Error('结束日期不能为空')))
  endDate: string;
}

/**
 * 评价统计查询DTO
 */
export class ReviewStatisticsDto {
  @Rule(RuleType.string().optional().error(new Error('开始日期格式错误')))
  startDate?: string;

  @Rule(RuleType.string().optional().error(new Error('结束日期格式错误')))
  endDate?: string;
}

/**
 * 员工评分排行查询DTO
 */
export class EmployeeRankingDto {
  @Rule(
    RuleType.number()
      .optional()
      .min(1)
      .max(100)
      .error(new Error('返回条数必须在1-100之间'))
  )
  limit?: number;

  @Rule(RuleType.string().optional().error(new Error('开始日期格式错误')))
  startDate?: string;

  @Rule(RuleType.string().optional().error(new Error('结束日期格式错误')))
  endDate?: string;
}
